package domain

import (
	"time"
)

// ============================================================================
// GARAGE MODELS - Forever Plan Architecture Compliant
// ============================================================================

// VehicleMake represents a vehicle manufacturer (BMW, Toyota, etc.)
type VehicleMake struct {
	ID        int       `json:"id" gorm:"primaryKey;autoIncrement"`
	Name      string    `json:"name" gorm:"not null;size:100"`
	Country   string    `json:"country" gorm:"size:100"`
	LogoURL   string    `json:"logo_url" gorm:"size:500"`
	IsActive  bool      `json:"is_active" gorm:"default:true"`
	IsDeleted bool      `json:"is_deleted" gorm:"default:false"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// VehicleModel represents a specific vehicle model (335i, Camry, etc.)
type VehicleModel struct {
	ID         int       `json:"id" gorm:"primaryKey;autoIncrement"`
	MakeID     int       `json:"make_id" gorm:"not null;index"`
	Name       string    `json:"name" gorm:"not null;size:100"`
	Generation string    `json:"generation" gorm:"size:50"`
	BodyType   string    `json:"body_type" gorm:"size:50"` // Sedan, SUV, Hatchback
	FuelType   string    `json:"fuel_type" gorm:"size:50"` // Gasoline, Diesel, Hybrid
	YearStart  int       `json:"year_start"`
	YearEnd    *int      `json:"year_end"` // NULL means still in production
	IsCurrent  bool      `json:"is_current" gorm:"default:true"`
	IsDeleted  bool      `json:"is_deleted" gorm:"default:false"`
	CreatedAt  time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt  time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Relations
	Make VehicleMake `json:"make" gorm:"foreignKey:MakeID"`
}

// VehicleYear represents specific years for each model
type VehicleYear struct {
	ID        int       `json:"id" gorm:"primaryKey;autoIncrement"`
	ModelID   int       `json:"model_id" gorm:"not null;index"`
	Year      int       `json:"year" gorm:"not null;index"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`

	// Relations
	Model VehicleModel `json:"model" gorm:"foreignKey:ModelID"`
}

// VehicleTrim represents trim levels (Base, Sport, Luxury, etc.)
type VehicleTrim struct {
	ID               int       `json:"id" gorm:"primaryKey;autoIncrement"`
	ModelID          int       `json:"model_id" gorm:"not null;index"`
	Name             string    `json:"name" gorm:"not null;size:100"`
	TrimLevel        string    `json:"trim_level" gorm:"size:50"` // Base, Mid, High, Luxury
	Doors            int       `json:"doors"`
	Seats            int       `json:"seats"`
	TransmissionType string    `json:"transmission_type" gorm:"size:50"` // Manual, Automatic, CVT
	DriveType        string    `json:"drive_type" gorm:"size:50"`        // FWD, RWD, AWD
	IsCurrent        bool      `json:"is_current" gorm:"default:true"`
	IsDeleted        bool      `json:"is_deleted" gorm:"default:false"`
	CreatedAt        time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt        time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Relations
	Model VehicleModel `json:"model" gorm:"foreignKey:ModelID"`
}

// VehicleEngine represents engine specifications
type VehicleEngine struct {
	ID            int       `json:"id" gorm:"primaryKey;autoIncrement"`
	ModelID       int       `json:"model_id" gorm:"not null;index"`
	TrimID        *int      `json:"trim_id" gorm:"index"` // Optional - some engines span multiple trims
	EngineCode    string    `json:"engine_code" gorm:"size:50"`
	Displacement  int       `json:"displacement_cc"` // Engine displacement in CC
	Cylinders     int       `json:"cylinders"`
	PowerHP       int       `json:"power_hp"`
	TorqueNM      int       `json:"torque_nm"`
	FuelType      string    `json:"fuel_type" gorm:"size:50"` // Gasoline, Diesel, Hybrid, Electric
	IsTurbo       bool      `json:"is_turbo" gorm:"default:false"`
	EngineFamily  string    `json:"engine_family" gorm:"size:100"`
	ValveTrain    string    `json:"valve_train" gorm:"size:50"` // SOHC, DOHC, etc.
	IsDeleted     bool      `json:"is_deleted" gorm:"default:false"`
	CreatedAt     time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt     time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Relations
	Model VehicleModel `json:"model" gorm:"foreignKey:ModelID"`
	Trim  *VehicleTrim `json:"trim,omitempty" gorm:"foreignKey:TrimID"`
}

// VehicleImage represents vehicle images
type VehicleImage struct {
	ID        int       `json:"id" gorm:"primaryKey;autoIncrement"`
	ModelID   int       `json:"model_id" gorm:"not null;index"`
	ImageURL  string    `json:"image_url" gorm:"not null;size:500"`
	ImageType string    `json:"image_type" gorm:"size:50"` // exterior, interior, engine
	ViewAngle string    `json:"view_angle" gorm:"size:50"` // front, rear, side, dashboard
	IsPrimary bool      `json:"is_primary" gorm:"default:false"`
	Caption   string    `json:"caption" gorm:"size:200"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`

	// Relations
	Model VehicleModel `json:"model" gorm:"foreignKey:ModelID"`
}

// UserVehicle represents a user's vehicle in their garage
type UserVehicle struct {
	ID              int       `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID          string    `json:"user_id" gorm:"not null;index;size:255"`
	MakeRefID       int       `json:"make_ref_id" gorm:"not null;index"`
	ModelRefID      int       `json:"model_ref_id" gorm:"not null;index"`
	YearRefID       *int      `json:"year_ref_id" gorm:"index"`
	TrimRefID       *int      `json:"trim_ref_id" gorm:"index"`
	EngineRefID     *int      `json:"engine_ref_id" gorm:"index"`
	
	// Legacy fields (for backward compatibility)
	Make         string `json:"make" gorm:"size:100"`
	Model        string `json:"model" gorm:"size:100"`
	Year         int    `json:"year"`
	Trim         string `json:"trim" gorm:"size:100"`
	BodyStyle    string `json:"body_style" gorm:"size:50"`
	Engine       string `json:"engine" gorm:"size:100"`
	Transmission string `json:"transmission" gorm:"size:50"`
	FuelType     string `json:"fuel_type" gorm:"size:50"`
	
	// Vehicle details
	Color           string     `json:"color" gorm:"size:50"`
	VIN             string     `json:"vin" gorm:"size:17"`
	LicensePlate    string     `json:"license_plate" gorm:"size:20"`
	Mileage         int        `json:"mileage"`
	Notes           string     `json:"notes" gorm:"type:text"`
	
	// New enhanced fields
	PurchaseDate    *time.Time `json:"purchase_date"`
	PurchasePrice   *float64   `json:"purchase_price" gorm:"type:decimal(15,2)"`
	InsuranceExpiry *time.Time `json:"insurance_expiry"`
	LastServiceDate *time.Time `json:"last_service_date"`
	NextServiceDue  *time.Time `json:"next_service_due"`
	Condition       string     `json:"condition" gorm:"size:50;default:good"` // excellent, good, fair, poor
	ImageURLs       []string   `json:"image_urls" gorm:"type:text[]"`
	
	// Status fields
	IsPrimary bool      `json:"is_primary" gorm:"default:false"`
	IsActive  bool      `json:"is_active" gorm:"default:true"`
	IsDeleted bool      `json:"is_deleted" gorm:"default:false"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Relations
	MakeRef   VehicleMake   `json:"make_ref" gorm:"foreignKey:MakeRefID"`
	ModelRef  VehicleModel  `json:"model_ref" gorm:"foreignKey:ModelRefID"`
	YearRef   *VehicleYear  `json:"year_ref,omitempty" gorm:"foreignKey:YearRefID"`
	TrimRef   *VehicleTrim  `json:"trim_ref,omitempty" gorm:"foreignKey:TrimRefID"`
	EngineRef *VehicleEngine `json:"engine_ref,omitempty" gorm:"foreignKey:EngineRefID"`
}

// ============================================================================
// DTO MODELS FOR API RESPONSES
// ============================================================================

// VehicleMakeDTO for API responses
type VehicleMakeDTO struct {
	ID          int    `json:"id"`
	Name        string `json:"name"`
	Country     string `json:"country"`
	LogoURL     string `json:"logo_url"`
	ModelsCount int    `json:"models_count"`
}

// VehicleModelDTO for API responses
type VehicleModelDTO struct {
	ID         int    `json:"id"`
	MakeID     int    `json:"make_id"`
	Name       string `json:"name"`
	Generation string `json:"generation"`
	BodyType   string `json:"body_type"`
	FuelType   string `json:"fuel_type"`
	YearStart  int    `json:"year_start"`
	YearEnd    *int   `json:"year_end"`
	MakeName   string `json:"make_name"`
	YearsCount int    `json:"years_count"`
	TrimsCount int    `json:"trims_count"`
}

// VehicleYearDTO for API responses
type VehicleYearDTO struct {
	ID      int    `json:"id"`
	ModelID int    `json:"model_id"`
	Year    int    `json:"year"`
	Model   string `json:"model_name"`
	Make    string `json:"make_name"`
}

// VehicleTrimDTO for API responses
type VehicleTrimDTO struct {
	ID               int    `json:"id"`
	ModelID          int    `json:"model_id"`
	Name             string `json:"name"`
	TrimLevel        string `json:"trim_level"`
	Doors            int    `json:"doors"`
	Seats            int    `json:"seats"`
	TransmissionType string `json:"transmission_type"`
	DriveType        string `json:"drive_type"`
	EnginesCount     int    `json:"engines_count"`
}

// VehicleEngineDTO for API responses
type VehicleEngineDTO struct {
	ID           int    `json:"id"`
	ModelID      int    `json:"model_id"`
	TrimID       *int   `json:"trim_id"`
	EngineCode   string `json:"engine_code"`
	Displacement int    `json:"displacement_cc"`
	Cylinders    int    `json:"cylinders"`
	PowerHP      int    `json:"power_hp"`
	TorqueNM     int    `json:"torque_nm"`
	FuelType     string `json:"fuel_type"`
	IsTurbo      bool   `json:"is_turbo"`
	EngineFamily string `json:"engine_family"`
	ValveTrain   string `json:"valve_train"`
	Description  string `json:"description"` // Generated description
}

// UserVehicleDTO for API responses
type UserVehicleDTO struct {
	ID              int        `json:"id"`
	UserID          string     `json:"user_id"`
	Make            string     `json:"make"`
	Model           string     `json:"model"`
	Year            int        `json:"year"`
	Trim            string     `json:"trim"`
	Engine          string     `json:"engine"`
	Color           string     `json:"color"`
	VIN             string     `json:"vin"`
	LicensePlate    string     `json:"license_plate"`
	Mileage         int        `json:"mileage"`
	Condition       string     `json:"condition"`
	PurchaseDate    *time.Time `json:"purchase_date"`
	PurchasePrice   *float64   `json:"purchase_price"`
	InsuranceExpiry *time.Time `json:"insurance_expiry"`
	LastServiceDate *time.Time `json:"last_service_date"`
	NextServiceDue  *time.Time `json:"next_service_due"`
	ImageURLs       []string   `json:"image_urls"`
	IsPrimary       bool       `json:"is_primary"`
	IsActive        bool       `json:"is_active"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
	
	// Enhanced fields with reference data
	MakeRef   *VehicleMakeDTO   `json:"make_ref,omitempty"`
	ModelRef  *VehicleModelDTO  `json:"model_ref,omitempty"`
	YearRef   *VehicleYearDTO   `json:"year_ref,omitempty"`
	TrimRef   *VehicleTrimDTO   `json:"trim_ref,omitempty"`
	EngineRef *VehicleEngineDTO `json:"engine_ref,omitempty"`
}

// ============================================================================
// REQUEST MODELS FOR API INPUTS
// ============================================================================

// CreateUserVehicleRequest for adding a new vehicle
type CreateUserVehicleRequest struct {
	MakeRefID       int        `json:"make_ref_id" binding:"required"`
	ModelRefID      int        `json:"model_ref_id" binding:"required"`
	YearRefID       *int       `json:"year_ref_id"`
	TrimRefID       *int       `json:"trim_ref_id"`
	EngineRefID     *int       `json:"engine_ref_id"`
	Color           string     `json:"color"`
	VIN             string     `json:"vin"`
	LicensePlate    string     `json:"license_plate"`
	Mileage         int        `json:"mileage"`
	Notes           string     `json:"notes"`
	PurchaseDate    *time.Time `json:"purchase_date"`
	PurchasePrice   *float64   `json:"purchase_price"`
	InsuranceExpiry *time.Time `json:"insurance_expiry"`
	LastServiceDate *time.Time `json:"last_service_date"`
	NextServiceDue  *time.Time `json:"next_service_due"`
	Condition       string     `json:"condition"`
	ImageURLs       []string   `json:"image_urls"`
	IsPrimary       bool       `json:"is_primary"`
}

// UpdateUserVehicleRequest for updating an existing vehicle
type UpdateUserVehicleRequest struct {
	Color           *string    `json:"color"`
	VIN             *string    `json:"vin"`
	LicensePlate    *string    `json:"license_plate"`
	Mileage         *int       `json:"mileage"`
	Notes           *string    `json:"notes"`
	PurchaseDate    *time.Time `json:"purchase_date"`
	PurchasePrice   *float64   `json:"purchase_price"`
	InsuranceExpiry *time.Time `json:"insurance_expiry"`
	LastServiceDate *time.Time `json:"last_service_date"`
	NextServiceDue  *time.Time `json:"next_service_due"`
	Condition       *string    `json:"condition"`
	ImageURLs       []string   `json:"image_urls"`
	IsPrimary       *bool      `json:"is_primary"`
	IsActive        *bool      `json:"is_active"`
}
