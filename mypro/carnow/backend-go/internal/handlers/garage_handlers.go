package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	domain "carnow-backend/internal/core/domain"
)

// GarageHandler handles garage-related API endpoints
type GarageHandler struct {
	DB *gorm.DB
}

// NewGarageHandler creates a new garage handler
func NewGarageHandler(db *gorm.DB) *GarageHandler {
	return &GarageHandler{
		DB: db,
	}
}

// ============================================================================
// VEHICLE MAKES ENDPOINTS
// ============================================================================

// GetVehicleMakes retrieves all vehicle makes with optional filtering
// GET /api/v1/garage/makes
func (h *GarageHandler) GetVehicleMakes(c *gin.Context) {
	var makes []domain.VehicleMake
	var makeDTOs []domain.VehicleMakeDTO

	// Query parameters
	search := c.Query("search")
	country := c.Query("country")
	limit := 50 // Default limit
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	// Build query
	query := h.DB.Where("is_deleted = ? AND is_active = ?", false, true)

	if search != "" {
		query = query.Where("name ILIKE ?", "%"+search+"%")
	}

	if country != "" {
		query = query.Where("country = ?", country)
	}

	// Execute query with model count
	err := query.
		Select("vehicle_makes.*, COUNT(vehicle_models.id) as models_count").
		Joins("LEFT JOIN vehicle_models ON vehicle_makes.id = vehicle_models.make_id AND vehicle_models.is_deleted = false").
		Group("vehicle_makes.id").
		Order("vehicle_makes.name ASC").
		Limit(limit).
		Find(&makes).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to fetch vehicle makes",
			"details": err.Error(),
		})
		return
	}

	// Convert to DTOs
	for _, make := range makes {
		makeDTOs = append(makeDTOs, domain.VehicleMakeDTO{
			ID:          make.ID,
			Name:        make.Name,
			Country:     make.Country,
			LogoURL:     make.LogoURL,
			ModelsCount: 0, // Will be populated by the query
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"makes": makeDTOs,
			"total": len(makeDTOs),
		},
	})
}

// ============================================================================
// VEHICLE MODELS ENDPOINTS
// ============================================================================

// GetVehicleModels retrieves models for a specific make
// GET /api/v1/garage/models/:make_id
func (h *GarageHandler) GetVehicleModels(c *gin.Context) {
	makeIDStr := c.Param("make_id")
	makeID, err := strconv.Atoi(makeIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid make ID",
		})
		return
	}

	var models []domain.VehicleModel
	var modelDTOs []domain.VehicleModelDTO

	// Query parameters
	search := c.Query("search")
	bodyType := c.Query("body_type")
	fuelType := c.Query("fuel_type")
	limit := 50
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	// Build query
	query := h.DB.Where("make_id = ? AND is_deleted = ?", makeID, false)

	if search != "" {
		query = query.Where("name ILIKE ?", "%"+search+"%")
	}

	if bodyType != "" {
		query = query.Where("body_type = ?", bodyType)
	}

	if fuelType != "" {
		query = query.Where("fuel_type = ?", fuelType)
	}

	// Execute query with counts
	err = query.
		Preload("Make").
		Order("name ASC").
		Limit(limit).
		Find(&models).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to fetch vehicle models",
			"details": err.Error(),
		})
		return
	}

	// Convert to DTOs and get counts
	for _, model := range models {
		// Count years
		var yearsCount int64
		h.DB.Model(&domain.VehicleYear{}).Where("model_id = ?", model.ID).Count(&yearsCount)

		// Count trims
		var trimsCount int64
		h.DB.Model(&domain.VehicleTrim{}).Where("model_id = ? AND is_deleted = ?", model.ID, false).Count(&trimsCount)

		modelDTOs = append(modelDTOs, domain.VehicleModelDTO{
			ID:         model.ID,
			MakeID:     model.MakeID,
			Name:       model.Name,
			Generation: model.Generation,
			BodyType:   model.BodyType,
			FuelType:   model.FuelType,
			YearStart:  model.YearStart,
			YearEnd:    model.YearEnd,
			MakeName:   model.Make.Name,
			YearsCount: int(yearsCount),
			TrimsCount: int(trimsCount),
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"models": modelDTOs,
			"total":  len(modelDTOs),
		},
	})
}

// ============================================================================
// VEHICLE YEARS ENDPOINTS
// ============================================================================

// GetVehicleYears retrieves years for a specific model
// GET /api/v1/garage/years/:model_id
func (h *GarageHandler) GetVehicleYears(c *gin.Context) {
	modelIDStr := c.Param("model_id")
	modelID, err := strconv.Atoi(modelIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid model ID",
		})
		return
	}

	var years []domain.VehicleYear
	var yearDTOs []domain.VehicleYearDTO

	// Execute query
	err = h.DB.
		Where("model_id = ?", modelID).
		Preload("Model").
		Preload("Model.Make").
		Order("year DESC").
		Find(&years).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to fetch vehicle years",
			"details": err.Error(),
		})
		return
	}

	// Convert to DTOs
	for _, year := range years {
		yearDTOs = append(yearDTOs, domain.VehicleYearDTO{
			ID:      year.ID,
			ModelID: year.ModelID,
			Year:    year.Year,
			Model:   year.Model.Name,
			Make:    year.Model.Make.Name,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"years": yearDTOs,
			"total": len(yearDTOs),
		},
	})
}

// ============================================================================
// VEHICLE TRIMS ENDPOINTS
// ============================================================================

// GetVehicleTrims retrieves trims for a specific model
// GET /api/v1/garage/trims/:model_id
func (h *GarageHandler) GetVehicleTrims(c *gin.Context) {
	modelIDStr := c.Param("model_id")
	modelID, err := strconv.Atoi(modelIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid model ID",
		})
		return
	}

	var trims []domain.VehicleTrim
	var trimDTOs []domain.VehicleTrimDTO

	// Query parameters
	trimLevel := c.Query("trim_level")
	transmissionType := c.Query("transmission_type")

	// Build query
	query := h.DB.Where("model_id = ? AND is_deleted = ?", modelID, false)

	if trimLevel != "" {
		query = query.Where("trim_level = ?", trimLevel)
	}

	if transmissionType != "" {
		query = query.Where("transmission_type = ?", transmissionType)
	}

	// Execute query
	err = query.
		Order("trim_level ASC, name ASC").
		Find(&trims).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to fetch vehicle trims",
			"details": err.Error(),
		})
		return
	}

	// Convert to DTOs and get engine counts
	for _, trim := range trims {
		// Count engines
		var enginesCount int64
		h.DB.Model(&domain.VehicleEngine{}).Where("model_id = ? AND (trim_id = ? OR trim_id IS NULL) AND is_deleted = ?", modelID, trim.ID, false).Count(&enginesCount)

		trimDTOs = append(trimDTOs, domain.VehicleTrimDTO{
			ID:               trim.ID,
			ModelID:          trim.ModelID,
			Name:             trim.Name,
			TrimLevel:        trim.TrimLevel,
			Doors:            trim.Doors,
			Seats:            trim.Seats,
			TransmissionType: trim.TransmissionType,
			DriveType:        trim.DriveType,
			EnginesCount:     int(enginesCount),
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"trims": trimDTOs,
			"total": len(trimDTOs),
		},
	})
}

// ============================================================================
// VEHICLE ENGINES ENDPOINTS
// ============================================================================

// GetVehicleEngines retrieves engines for a specific model
// GET /api/v1/garage/engines/:model_id
func (h *GarageHandler) GetVehicleEngines(c *gin.Context) {
	modelIDStr := c.Param("model_id")
	modelID, err := strconv.Atoi(modelIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid model ID",
		})
		return
	}

	var engines []domain.VehicleEngine
	var engineDTOs []domain.VehicleEngineDTO

	// Query parameters
	fuelType := c.Query("fuel_type")
	isTurbo := c.Query("is_turbo")
	trimIDStr := c.Query("trim_id")

	// Build query
	query := h.DB.Where("model_id = ? AND is_deleted = ?", modelID, false)

	if fuelType != "" {
		query = query.Where("fuel_type = ?", fuelType)
	}

	if isTurbo != "" {
		if isTurbo == "true" {
			query = query.Where("is_turbo = ?", true)
		} else if isTurbo == "false" {
			query = query.Where("is_turbo = ?", false)
		}
	}

	if trimIDStr != "" {
		if trimID, err := strconv.Atoi(trimIDStr); err == nil {
			query = query.Where("trim_id = ?", trimID)
		}
	}

	// Execute query
	err = query.
		Order("displacement_cc ASC, power_hp DESC").
		Find(&engines).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to fetch vehicle engines",
			"details": err.Error(),
		})
		return
	}

	// Convert to DTOs
	for _, engine := range engines {
		// Generate description
		description := generateEngineDescription(engine)

		engineDTOs = append(engineDTOs, domain.VehicleEngineDTO{
			ID:           engine.ID,
			ModelID:      engine.ModelID,
			TrimID:       engine.TrimID,
			EngineCode:   engine.EngineCode,
			Displacement: engine.Displacement,
			Cylinders:    engine.Cylinders,
			PowerHP:      engine.PowerHP,
			TorqueNM:     engine.TorqueNM,
			FuelType:     engine.FuelType,
			IsTurbo:      engine.IsTurbo,
			EngineFamily: engine.EngineFamily,
			ValveTrain:   engine.ValveTrain,
			Description:  description,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"engines": engineDTOs,
			"total":   len(engineDTOs),
		},
	})
}

// generateEngineDescription creates a human-readable engine description
func generateEngineDescription(engine domain.VehicleEngine) string {
	description := ""

	if engine.Displacement > 0 {
		description += strconv.FormatFloat(float64(engine.Displacement)/1000.0, 'f', 1, 64) + "L "
	}

	if engine.Cylinders > 0 {
		description += "V" + strconv.Itoa(engine.Cylinders) + " "
	}

	if engine.IsTurbo {
		description += "Turbo "
	}

	description += engine.FuelType

	if engine.PowerHP > 0 {
		description += " (" + strconv.Itoa(engine.PowerHP) + "HP)"
	}

	return description
}

// ============================================================================
// USER VEHICLES ENDPOINTS
// ============================================================================

// GetMyVehicles retrieves all vehicles for the authenticated user
// GET /api/v1/garage/my-vehicles
func (h *GarageHandler) GetMyVehicles(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	var vehicles []domain.UserVehicle
	var vehicleDTOs []domain.UserVehicleDTO

	// Query parameters
	isActive := c.DefaultQuery("is_active", "true")
	isPrimary := c.Query("is_primary")

	// Build query
	query := h.DB.Where("user_id = ? AND is_deleted = ?", userID, false)

	if isActive == "true" {
		query = query.Where("is_active = ?", true)
	} else if isActive == "false" {
		query = query.Where("is_active = ?", false)
	}

	if isPrimary != "" {
		if isPrimary == "true" {
			query = query.Where("is_primary = ?", true)
		} else if isPrimary == "false" {
			query = query.Where("is_primary = ?", false)
		}
	}

	// Execute query with preloads
	err := query.
		Preload("MakeRef").
		Preload("ModelRef").
		Preload("ModelRef.Make").
		Preload("YearRef").
		Preload("YearRef.Model").
		Preload("YearRef.Model.Make").
		Preload("TrimRef").
		Preload("EngineRef").
		Order("is_primary DESC, created_at DESC").
		Find(&vehicles).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to fetch user vehicles",
			"details": err.Error(),
		})
		return
	}

	// Convert to DTOs
	for _, vehicle := range vehicles {
		dto := convertUserVehicleToDTO(vehicle)
		vehicleDTOs = append(vehicleDTOs, dto)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"vehicles": vehicleDTOs,
			"total":    len(vehicleDTOs),
		},
	})
}

// CreateUserVehicle creates a new vehicle for the user
// POST /api/v1/garage/vehicles
func (h *GarageHandler) CreateUserVehicle(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	var req domain.CreateUserVehicleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Validate references exist
	if err := h.validateVehicleReferences(&req.MakeRefID, &req.ModelRefID, req.YearRefID, req.TrimRefID, req.EngineRefID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid vehicle references",
			"details": err.Error(),
		})
		return
	}

	// Get reference data for legacy fields
	makeRef, modelRef, yearRef, err := h.getVehicleReferenceData(&req.MakeRefID, &req.ModelRefID, req.YearRefID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get reference data",
			"details": err.Error(),
		})
		return
	}

	// If this is set as primary, unset other primary vehicles
	if req.IsPrimary {
		h.DB.Model(&domain.UserVehicle{}).
			Where("user_id = ? AND is_deleted = ?", userID, false).
			Update("is_primary", false)
	}

	// Create vehicle
	vehicle := domain.UserVehicle{
		UserID:      userID.(string),
		MakeRefID:   req.MakeRefID,
		ModelRefID:  req.ModelRefID,
		YearRefID:   req.YearRefID,
		TrimRefID:   req.TrimRefID,
		EngineRefID: req.EngineRefID,

		// Legacy fields for backward compatibility
		Make:  makeRef.Name,
		Model: modelRef.Name,
		Year:  yearRef.Year,

		// User-provided fields
		Color:           req.Color,
		VIN:             req.VIN,
		LicensePlate:    req.LicensePlate,
		Mileage:         req.Mileage,
		Notes:           req.Notes,
		PurchaseDate:    req.PurchaseDate,
		PurchasePrice:   req.PurchasePrice,
		InsuranceExpiry: req.InsuranceExpiry,
		LastServiceDate: req.LastServiceDate,
		NextServiceDue:  req.NextServiceDue,
		Condition:       req.Condition,
		ImageURLs:       req.ImageURLs,
		IsPrimary:       req.IsPrimary,
		IsActive:        true,
		IsDeleted:       false,
	}

	if err := h.DB.Create(&vehicle).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to create vehicle",
			"details": err.Error(),
		})
		return
	}

	// Load the created vehicle with all relations
	h.DB.
		Preload("MakeRef").
		Preload("ModelRef").
		Preload("ModelRef.Make").
		Preload("YearRef").
		Preload("YearRef.Model").
		Preload("YearRef.Model.Make").
		Preload("TrimRef").
		Preload("EngineRef").
		First(&vehicle, vehicle.ID)

	dto := convertUserVehicleToDTO(vehicle)

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Vehicle created successfully",
		"data":    dto,
	})
}

// UpdateUserVehicle updates an existing user vehicle
// PUT /api/v1/garage/vehicles/:id
func (h *GarageHandler) UpdateUserVehicle(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	vehicleIDStr := c.Param("id")
	vehicleID, err := strconv.Atoi(vehicleIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid vehicle ID",
		})
		return
	}

	var req domain.UpdateUserVehicleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Find existing vehicle
	var vehicle domain.UserVehicle
	err = h.DB.Where("id = ? AND user_id = ? AND is_deleted = ?", vehicleID, userID, false).First(&vehicle).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Vehicle not found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to find vehicle",
				"details": err.Error(),
			})
		}
		return
	}

	// If this is set as primary, unset other primary vehicles
	if req.IsPrimary != nil && *req.IsPrimary && !vehicle.IsPrimary {
		h.DB.Model(&domain.UserVehicle{}).
			Where("user_id = ? AND is_deleted = ?", userID, false).
			Update("is_primary", false)
	}

	// Update fields
	updates := make(map[string]interface{})
	if req.Color != nil {
		updates["color"] = *req.Color
	}
	if req.VIN != nil {
		updates["vin"] = *req.VIN
	}
	if req.LicensePlate != nil {
		updates["license_plate"] = *req.LicensePlate
	}
	if req.Mileage != nil {
		updates["mileage"] = *req.Mileage
	}
	if req.Notes != nil {
		updates["notes"] = *req.Notes
	}
	if req.PurchaseDate != nil {
		updates["purchase_date"] = *req.PurchaseDate
	}
	if req.PurchasePrice != nil {
		updates["purchase_price"] = *req.PurchasePrice
	}
	if req.InsuranceExpiry != nil {
		updates["insurance_expiry"] = *req.InsuranceExpiry
	}
	if req.LastServiceDate != nil {
		updates["last_service_date"] = *req.LastServiceDate
	}
	if req.NextServiceDue != nil {
		updates["next_service_due"] = *req.NextServiceDue
	}
	if req.Condition != nil {
		updates["condition"] = *req.Condition
	}
	if req.ImageURLs != nil {
		updates["image_urls"] = req.ImageURLs
	}
	if req.IsPrimary != nil {
		updates["is_primary"] = *req.IsPrimary
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	updates["updated_at"] = time.Now()

	// Perform update
	if err := h.DB.Model(&vehicle).Updates(updates).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to update vehicle",
			"details": err.Error(),
		})
		return
	}

	// Load updated vehicle with relations
	h.DB.
		Preload("MakeRef").
		Preload("ModelRef").
		Preload("ModelRef.Make").
		Preload("YearRef").
		Preload("YearRef.Model").
		Preload("YearRef.Model.Make").
		Preload("TrimRef").
		Preload("EngineRef").
		First(&vehicle, vehicle.ID)

	dto := convertUserVehicleToDTO(vehicle)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Vehicle updated successfully",
		"data":    dto,
	})
}

// DeleteUserVehicle soft deletes a user vehicle
// DELETE /api/v1/garage/vehicles/:id
func (h *GarageHandler) DeleteUserVehicle(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	vehicleIDStr := c.Param("id")
	vehicleID, err := strconv.Atoi(vehicleIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid vehicle ID",
		})
		return
	}

	// Find existing vehicle
	var vehicle domain.UserVehicle
	err = h.DB.Where("id = ? AND user_id = ? AND is_deleted = ?", vehicleID, userID, false).First(&vehicle).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Vehicle not found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to find vehicle",
				"details": err.Error(),
			})
		}
		return
	}

	// Soft delete
	updates := map[string]interface{}{
		"is_deleted": true,
		"is_active":  false,
		"is_primary": false,
		"updated_at": time.Now(),
		"deleted_at": time.Now(),
	}

	if err := h.DB.Model(&vehicle).Updates(updates).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to delete vehicle",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Vehicle deleted successfully",
	})
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

// validateVehicleReferences validates that all vehicle references exist
func (h *GarageHandler) validateVehicleReferences(makeRefID, modelRefID, yearRefID *int, trimRefID, engineRefID *int) error {
	// Validate make
	if makeRefID != nil {
		var count int64
		h.DB.Model(&domain.VehicleMake{}).Where("id = ? AND is_deleted = ? AND is_active = ?", *makeRefID, false, true).Count(&count)
		if count == 0 {
			return fmt.Errorf("invalid make reference ID: %d", *makeRefID)
		}
	}

	// Validate model
	if modelRefID != nil {
		var count int64
		query := h.DB.Model(&domain.VehicleModel{}).Where("id = ? AND is_deleted = ?", *modelRefID, false)
		if makeRefID != nil {
			query = query.Where("make_id = ?", *makeRefID)
		}
		query.Count(&count)
		if count == 0 {
			return fmt.Errorf("invalid model reference ID: %d", *modelRefID)
		}
	}

	// Validate year
	if yearRefID != nil {
		var count int64
		query := h.DB.Model(&domain.VehicleYear{}).Where("id = ?", *yearRefID)
		if modelRefID != nil {
			query = query.Where("model_id = ?", *modelRefID)
		}
		query.Count(&count)
		if count == 0 {
			return fmt.Errorf("invalid year reference ID: %d", *yearRefID)
		}
	}

	// Validate trim (optional)
	if trimRefID != nil {
		var count int64
		query := h.DB.Model(&domain.VehicleTrim{}).Where("id = ? AND is_deleted = ?", *trimRefID, false)
		if modelRefID != nil {
			query = query.Where("model_id = ?", *modelRefID)
		}
		query.Count(&count)
		if count == 0 {
			return fmt.Errorf("invalid trim reference ID: %d", *trimRefID)
		}
	}

	// Validate engine (optional)
	if engineRefID != nil {
		var count int64
		query := h.DB.Model(&domain.VehicleEngine{}).Where("id = ? AND is_deleted = ?", *engineRefID, false)
		if modelRefID != nil {
			query = query.Where("model_id = ?", *modelRefID)
		}
		if trimRefID != nil {
			query = query.Where("trim_id = ? OR trim_id IS NULL", *trimRefID)
		}
		query.Count(&count)
		if count == 0 {
			return fmt.Errorf("invalid engine reference ID: %d", *engineRefID)
		}
	}

	return nil
}

// getVehicleReferenceData retrieves reference data for legacy fields
func (h *GarageHandler) getVehicleReferenceData(makeRefID, modelRefID, yearRefID *int) (*domain.VehicleMake, *domain.VehicleModel, *domain.VehicleYear, error) {
	var makeRef domain.VehicleMake
	var modelRef domain.VehicleModel
	var yearRef domain.VehicleYear

	// Get make
	if makeRefID != nil {
		if err := h.DB.First(&makeRef, *makeRefID).Error; err != nil {
			return nil, nil, nil, fmt.Errorf("failed to get make reference: %w", err)
		}
	}

	// Get model
	if modelRefID != nil {
		if err := h.DB.First(&modelRef, *modelRefID).Error; err != nil {
			return nil, nil, nil, fmt.Errorf("failed to get model reference: %w", err)
		}
	}

	// Get year
	if yearRefID != nil {
		if err := h.DB.First(&yearRef, *yearRefID).Error; err != nil {
			return nil, nil, nil, fmt.Errorf("failed to get year reference: %w", err)
		}
	}

	return &makeRef, &modelRef, &yearRef, nil
}

// convertUserVehicleToDTO converts a UserVehicle to UserVehicleDTO
func convertUserVehicleToDTO(vehicle domain.UserVehicle) domain.UserVehicleDTO {
	// Build trim and engine descriptions from reference data
	trim := vehicle.Trim     // Use legacy field as fallback
	engine := vehicle.Engine // Use legacy field as fallback

	if vehicle.TrimRef != nil {
		trim = vehicle.TrimRef.Name
	}

	if vehicle.EngineRef != nil {
		engine = generateEngineDescription(*vehicle.EngineRef)
	}

	return domain.UserVehicleDTO{
		ID:              vehicle.ID,
		UserID:          vehicle.UserID,
		Make:            vehicle.Make,
		Model:           vehicle.Model,
		Year:            vehicle.Year,
		Trim:            trim,
		Engine:          engine,
		Color:           vehicle.Color,
		VIN:             vehicle.VIN,
		LicensePlate:    vehicle.LicensePlate,
		Mileage:         vehicle.Mileage,
		Condition:       vehicle.Condition,
		PurchaseDate:    vehicle.PurchaseDate,
		PurchasePrice:   vehicle.PurchasePrice,
		InsuranceExpiry: vehicle.InsuranceExpiry,
		LastServiceDate: vehicle.LastServiceDate,
		NextServiceDue:  vehicle.NextServiceDue,
		ImageURLs:       vehicle.ImageURLs,
		IsPrimary:       vehicle.IsPrimary,
		IsActive:        vehicle.IsActive,
		CreatedAt:       vehicle.CreatedAt,
		UpdatedAt:       vehicle.UpdatedAt,
	}
}
