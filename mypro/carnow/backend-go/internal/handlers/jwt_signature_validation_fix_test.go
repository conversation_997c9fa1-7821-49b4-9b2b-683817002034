package handlers

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"carnow-backend/internal/config"
	"carnow-backend/internal/shared/services"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestJWTSignatureValidationFix tests the JWT signature validation fix for Task 9
func TestJWTSignatureValidationFix(t *testing.T) {
	// Create test configuration with consistent JWT secret
	cfg := &config.Config{
		JWT: config.JWTConfig{
			Secret:           "cmk11gkJgFX6BKiIOioVpWfHY4ZZ7RRR6xTWyy7/wOlK6ZoRyFYlS+2hyQIS+OBt3ef5D1z9HVFYWoDJwwD4eQ==",
			Issuer:           "carnow-backend",
			Audience:         "carnow-app",
			Algorithm:        "HS256",
			ExpiresIn:        900 * time.Second,  // 15 minutes
			RefreshExpiresIn: 3600 * time.Second, // 1 hour
		},
		Supabase: config.SupabaseConfig{
			URL:       "https://lpxtghyvxuenyyisrrro.supabase.co",
			AnonKey:   "test-anon-key",
			JWTSecret: "cmk11gkJgFX6BKiIOioVpWfHY4ZZ7RRR6xTWyy7/wOlK6ZoRyFYlS+2hyQIS+OBt3ef5D1z9HVFYWoDJwwD4eQ==",
		},
		Google: config.GoogleConfig{
			ClientID:     "test-client-id",
			ClientSecret: "test-client-secret",
		},
	}

	// Test 1: Verify JWT service creates consistent tokens
	t.Run("JWT Service Consistency", func(t *testing.T) {
		// Create JWT service
		jwtService, err := services.NewJWTService(cfg)
		require.NoError(t, err)

		// Generate enhanced JWT token
		token, err := jwtService.GenerateEnhancedJWT(
			"101506588471475152713",
			"<EMAIL>",
			"Attia Ibrahim",
			"google",
			true,
		)
		require.NoError(t, err)
		assert.NotEmpty(t, token)

		// Validate the same token with the same service
		claims, err := jwtService.ValidateEnhancedToken(token)
		require.NoError(t, err)
		assert.Equal(t, "101506588471475152713", claims.UserID)
		assert.Equal(t, "<EMAIL>", claims.Email)
		assert.Equal(t, "Attia Ibrahim", claims.Name)
		assert.Equal(t, "google", claims.Provider)
		assert.True(t, claims.IsAdmin)
	})

	// Test 2: Verify configuration consistency
	t.Run("Configuration Consistency", func(t *testing.T) {
		// Verify JWT secret matches Supabase JWT secret
		assert.Equal(t, cfg.Supabase.JWTSecret, cfg.JWT.Secret)

		// Verify JWT service uses the correct secret
		jwtService, err := services.NewJWTService(cfg)
		require.NoError(t, err)

		// Generate and validate token to ensure secret consistency
		token, err := jwtService.GenerateEnhancedJWT(
			"test-user-id",
			"<EMAIL>",
			"Test User",
			"google",
			false,
		)
		require.NoError(t, err)

		claims, err := jwtService.ValidateEnhancedToken(token)
		require.NoError(t, err)
		assert.Equal(t, "test-user-id", claims.UserID)
	})

	// Test 3: Verify error handling for invalid tokens
	t.Run("Invalid Token Handling", func(t *testing.T) {
		gin.SetMode(gin.TestMode)

		// Create test router with JWT middleware
		router := gin.New()
		router.Use(JWTMiddleware(cfg))
		router.GET("/test", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"message": "success"})
		})

		// Test with invalid token
		req, err := http.NewRequest("GET", "/test", nil)
		require.NoError(t, err)
		req.Header.Set("Authorization", "Bearer invalid-token")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Should return 401 Unauthorized
		assert.Equal(t, http.StatusUnauthorized, w.Code)

		var response map[string]interface{}
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.Contains(t, response, "error")
	})
}

// TestJWTSecretConsistency tests that all JWT operations use the same secret
func TestJWTSecretConsistency(t *testing.T) {
	// Test configuration with both JWT and Supabase secrets
	cfg := &config.Config{
		JWT: config.JWTConfig{
			Secret:           "different-secret",
			Issuer:           "carnow-backend",
			Audience:         "carnow-app",
			Algorithm:        "HS256",
			ExpiresIn:        900 * time.Second,
			RefreshExpiresIn: 3600 * time.Second,
		},
		Supabase: config.SupabaseConfig{
			JWTSecret: "cmk11gkJgFX6BKiIOioVpWfHY4ZZ7RRR6xTWyy7/wOlK6ZoRyFYlS+2hyQIS+OBt3ef5D1z9HVFYWoDJwwD4eQ==",
		},
	}

	// Apply the configuration fix (this should happen in config.Load())
	if cfg.Supabase.JWTSecret != "" {
		cfg.JWT.Secret = cfg.Supabase.JWTSecret
	}

	// Verify the fix worked
	assert.Equal(t, cfg.Supabase.JWTSecret, cfg.JWT.Secret)

	// Create JWT service and verify it uses the correct secret
	jwtService, err := services.NewJWTService(cfg)
	require.NoError(t, err)

	// Generate token
	token, err := jwtService.GenerateEnhancedJWT(
		"test-user",
		"<EMAIL>",
		"Test User",
		"google",
		false,
	)
	require.NoError(t, err)

	// Validate token with the same service
	claims, err := jwtService.ValidateEnhancedToken(token)
	require.NoError(t, err)
	assert.Equal(t, "test-user", claims.UserID)
}

// TestJWTMiddlewareConsistency tests that middleware uses consistent JWT service
func TestJWTMiddlewareConsistency(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		JWT: config.JWTConfig{
			Secret:           "cmk11gkJgFX6BKiIOioVpWfHY4ZZ7RRR6xTWyy7/wOlK6ZoRyFYlS+2hyQIS+OBt3ef5D1z9HVFYWoDJwwD4eQ==",
			Issuer:           "carnow-backend",
			Audience:         "carnow-app",
			Algorithm:        "HS256",
			ExpiresIn:        900 * time.Second,
			RefreshExpiresIn: 3600 * time.Second,
		},
		Supabase: config.SupabaseConfig{
			URL:       "https://lpxtghyvxuenyyisrrro.supabase.co",
			AnonKey:   "test-anon-key",
			JWTSecret: "cmk11gkJgFX6BKiIOioVpWfHY4ZZ7RRR6xTWyy7/wOlK6ZoRyFYlS+2hyQIS+OBt3ef5D1z9HVFYWoDJwwD4eQ==",
		},
	}

	// Create JWT service for token generation
	jwtService, err := services.NewJWTService(cfg)
	require.NoError(t, err)

	// Generate test token
	token, err := jwtService.GenerateEnhancedJWT(
		"101506588471475152713",
		"<EMAIL>",
		"Attia Ibrahim",
		"google",
		true,
	)
	require.NoError(t, err)

	// Create test router with JWT middleware
	router := gin.New()
	router.Use(JWTMiddleware(cfg))
	router.GET("/test", func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "user_id not found"})
			return
		}
		c.JSON(http.StatusOK, gin.H{"user_id": userID})
	})

	// Test request with valid token
	req, err := http.NewRequest("GET", "/test", nil)
	require.NoError(t, err)
	req.Header.Set("Authorization", "Bearer "+token)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Should succeed with consistent JWT validation
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, "101506588471475152713", response["user_id"])
}
