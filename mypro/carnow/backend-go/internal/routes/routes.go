package routes

import (
	"carnow-backend/internal/config"
	"carnow-backend/internal/handlers"
	"carnow-backend/internal/middleware"
	"carnow-backend/internal/shared/services"

	"github.com/gin-gonic/gin"
)

// SetupRoutes configures all API routes with unified authentication
// Task 7: Enhanced with proper dependency injection and unified auth handlers
func SetupRoutes(
	router *gin.Engine,
	api *handlers.CleanAPI,
	jwtService *services.JWTService,
	googleService *services.GoogleOAuthService,
	config *config.Config,
	garageHandler *handlers.GarageHandler,
) {
	// Setup CSRF Protection
	csrfConfig := middleware.CarNowCSRFConfig()
	csrfProtection := middleware.NewCSRFProtection(csrfConfig)

	// API v1 group with CSRF protection
	v1 := router.Group("/api/v1")
	v1.Use(csrfProtection.Middleware())
	{
		// Health check (exempt from CSRF)
		v1.GET("/health", api.Health)

		// CSRF Protection endpoints (exempt from CSRF validation)
		csrf := v1.Group("/csrf")
		{
			csrf.GET("/token", api.GetCSRFToken)
			csrf.POST("/validate", api.ValidateCSRFToken)
			csrf.GET("/config", api.GetCSRFConfig)
			csrf.POST("/refresh", api.RefreshCSRFToken)
			csrf.GET("/status", api.GetCSRFStatus)
		}

		// =============================================================================
		// TASK 7: Unified Authentication Routes (Forever Plan Compliant)
		// =============================================================================

		// Create unified auth handlers with proper dependency injection
		unifiedAuth := handlers.NewUnifiedAuthHandlers(
			jwtService,
			googleService,
			config,
		)

		// Authentication routes - Task 7 Requirements
		auth := v1.Group("/auth")
		{
			// Task 7.1: POST /auth/login - Email/Password Authentication
			auth.POST("/login", unifiedAuth.Login)

			// Task 7.2: POST /auth/register - User Registration
			auth.POST("/register", unifiedAuth.Register)

			// Task 7.3: POST /auth/google - Google OAuth Token Verification
			auth.POST("/google", unifiedAuth.GoogleAuth)

			// Task 7.4: POST /auth/refresh - Token Renewal
			auth.POST("/refresh", unifiedAuth.RefreshToken)

			// Legacy endpoints (maintained for backward compatibility)
			auth.POST("/signin", api.SignIn) // Legacy: use /login instead
			auth.POST("/signup", api.SignUp) // Legacy: use /register instead
			auth.POST("/signout", api.SignOut)
			auth.POST("/reset-password", api.ResetPassword)
			auth.POST("/change-password", api.ChangePassword)

			// MFA (Multi-Factor Authentication) routes
			mfa := auth.Group("/mfa")
			{
				mfa.POST("/send-otp", api.SendMFAOTP)
				mfa.POST("/verify-otp", api.VerifyMFAOTP)
				mfa.POST("/setup-totp", api.SetupTOTP)
				mfa.POST("/confirm-totp", api.ConfirmTOTP)
				mfa.GET("/settings", api.GetMFASettings)
				mfa.PUT("/settings", api.UpdateMFASettings)
			}

			// Enhanced Session Management routes
			sessions := auth.Group("/sessions")
			{
				sessions.POST("", api.CreateSession)
				sessions.GET("", api.GetUserSessions)
				sessions.DELETE("", api.RevokeAllSessions)
				sessions.DELETE("/:session_id", api.RevokeSession)
				sessions.POST("/refresh", api.RefreshSession)
				sessions.POST("/validate", api.ValidateSession)
				sessions.GET("/activity", api.GetSessionActivity)
				sessions.POST("/fingerprint", api.GenerateDeviceFingerprint)
			}
		}

		// Categories routes
		categories := v1.Group("/categories")
		{
			categories.GET("", api.GetCategories)
			categories.GET("/:mainCategoryId/hierarchy", api.GetCategoryHierarchy)
		}

		// Main categories routes
		mainCategories := v1.Group("/main-categories")
		{
			mainCategories.GET("", api.GetMainCategories)
		}

		// Category attributes routes
		categoryAttributes := v1.Group("/category-attributes")
		{
			categoryAttributes.GET("/:mainCategoryId", api.GetCategoryAttributes)
			categoryAttributes.POST("", api.CreateCategoryAttribute)
			categoryAttributes.PUT("/:id", api.UpdateCategoryAttribute)
			categoryAttributes.DELETE("/:id", api.DeleteCategoryAttribute)
		}

		// Products routes
		products := v1.Group("/products")
		{
			products.GET("", api.GetProducts)
			products.GET("/search", api.SearchProducts)
			products.GET("/featured", api.GetFeaturedProducts)
			products.GET("/bestsellers", api.GetBestsellerProducts)
			products.GET("/:id", api.GetProduct)
			products.GET("/:id/attributes", api.GetProductAttributes)
			products.POST("", api.CreateProduct)
			products.PUT("/:id", api.UpdateProduct)
			products.DELETE("/:id", api.DeleteProduct)
		}

		// Product attributes routes
		productAttributes := v1.Group("/product-attributes")
		{
			productAttributes.POST("", api.CreateProductAttribute)
			productAttributes.PUT("/:id", api.UpdateProductAttribute)
			productAttributes.DELETE("/:id", api.DeleteProductAttribute)
		}

		// User profile routes
		profile := v1.Group("/profile")
		{
			profile.GET("", api.GetUserProfile)
			profile.PUT("", api.UpdateUserProfile)
		}

		// Wallet routes
		wallet := v1.Group("/wallet")
		{
			wallet.GET("", api.GetWallet)
			wallet.GET("/transactions", api.GetWalletTransactions)
			wallet.POST("/transactions", api.CreateWalletTransaction)
		}

		// Orders routes
		orders := v1.Group("/orders")
		{
			orders.GET("", api.GetUserOrders)
			orders.GET("/:id", api.GetOrder)
			orders.POST("", api.CreateOrder)
			orders.PUT("/:id", api.UpdateOrder)
			orders.DELETE("/:id", api.CancelOrder)
		}

		// Sellers routes
		sellers := v1.Group("/sellers")
		{
			sellers.GET("", api.GetAllSellers)
			sellers.GET("/:id", api.GetSeller)
			sellers.POST("", api.CreateSeller)
			sellers.PUT("/:id", api.UpdateSeller)
			sellers.PUT("/:id/status", api.UpdateSellerStatus)
			sellers.GET("/:id/documents", api.GetSellerDocuments)
			sellers.POST("/:id/documents", api.UploadSellerDocument)
		}

		// File storage routes
		storage := v1.Group("/storage")
		{
			storage.POST("/upload", api.UploadFile)
			storage.DELETE("/:filename", api.DeleteFile)
			storage.GET("/url/:filename", api.GetStorageURL)
		}

		// Analytics routes
		analytics := v1.Group("/analytics")
		{
			analytics.GET("/sales", api.GetSalesAnalytics)
			analytics.GET("/inventory", api.GetInventoryAnalytics)
		}

		// Cities routes
		cities := v1.Group("/cities")
		{
			cities.GET("", api.GetCities)
		}

		// =============================================================================
		// GARAGE ROUTES - Forever Plan Architecture
		// =============================================================================
		// Garage routes for vehicle management
		garage := v1.Group("/garage")
		{
			// Vehicle reference data endpoints
			garage.GET("/makes", garageHandler.GetVehicleMakes)
			garage.GET("/models/:make_id", garageHandler.GetVehicleModels)
			garage.GET("/years/:model_id", garageHandler.GetVehicleYears)
			garage.GET("/trims/:model_id", garageHandler.GetVehicleTrims)
			garage.GET("/engines/:model_id", garageHandler.GetVehicleEngines)

			// User vehicle management endpoints
			garage.GET("/my-vehicles", garageHandler.GetMyVehicles)
			garage.POST("/vehicles", garageHandler.CreateUserVehicle)
			garage.PUT("/vehicles/:id", garageHandler.UpdateUserVehicle)
			garage.DELETE("/vehicles/:id", garageHandler.DeleteUserVehicle)
		}
	}
}
