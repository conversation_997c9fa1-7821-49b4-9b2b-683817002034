package routes

import (
	"carnow-backend/internal/config"
	"carnow-backend/internal/handlers"
	"carnow-backend/internal/infrastructure/database"
	enhancedmiddleware "carnow-backend/internal/middleware"
	adminhandlers "carnow-backend/internal/modules/admin/handlers"
	adminrepositories "carnow-backend/internal/modules/admin/repositories"
	adminservices "carnow-backend/internal/modules/admin/services"
	orderhandlers "carnow-backend/internal/modules/orders/handlers"
	orderrepositories "carnow-backend/internal/modules/orders/repositories"
	orderservices "carnow-backend/internal/modules/orders/services"
	producthandlers "carnow-backend/internal/modules/products/handlers"
	productrepositories "carnow-backend/internal/modules/products/repositories"
	productservices "carnow-backend/internal/modules/products/services"
	"carnow-backend/internal/shared/middleware"
	"carnow-backend/internal/shared/services"
	"context"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jackc/pgx/v5"
	"go.uber.org/zap"
)

// SetupSecureRoutes sets up all routes with comprehensive security middleware
func SetupSecureRoutes(cfg *config.Config, db *database.Database, logger *zap.Logger) (*gin.Engine, error) {
	// Set Gin mode based on environment
	if cfg.App.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	router := gin.New()

	// Global middleware setup
	setupGlobalMiddleware(router, cfg, logger)

	// Setup API routes with security
	if err := setupAPIRoutes(router, cfg, db, logger); err != nil {
		return nil, err
	}

	return router, nil
}

// setupGlobalMiddleware configures global middleware for all routes
func setupGlobalMiddleware(router *gin.Engine, cfg *config.Config, logger *zap.Logger) {
	// Recovery middleware
	router.Use(gin.Recovery())

	// Custom logging middleware with default formatter
	router.Use(gin.Logger())

	// CORS middleware (if needed)
	router.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// Security headers middleware
	router.Use(func(c *gin.Context) {
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		c.Header("Content-Security-Policy", "default-src 'self'")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Next()
	})
}

// setupAPIRoutes configures API routes with appropriate security levels
func setupAPIRoutes(router *gin.Engine, cfg *config.Config, db *database.Database, logger *zap.Logger) error {
	// Create a SimpleDB adapter for handlers that expect it
	simpleDB := &database.SimpleDB{
		Pool:   nil, // We'll use the GORM DB for now
		Config: cfg,
	}

	// Create handlers
	authHandlers, err := handlers.NewSecureAuthHandlers(cfg, simpleDB)
	if err != nil {
		return err
	}

	// API v1 group
	v1 := router.Group("/api/v1")

	// Setup public routes (with rate limiting but no auth)
	setupPublicRoutes(v1, cfg, db, authHandlers, logger)

	// Setup authenticated routes (with auth middleware)
	setupAuthenticatedRoutes(v1, cfg, db, logger)

	// Setup admin routes (with admin auth)
	setupAdminRoutes(v1, cfg, db, logger)

	return nil
}

// setupPublicRoutes configures public API routes with basic security
func setupPublicRoutes(v1 *gin.RouterGroup, cfg *config.Config, db *database.Database, authHandlers *handlers.SecureAuthHandlers, logger *zap.Logger) {
	// Create a SimpleDB adapter for handlers that expect it
	simpleDB := &database.SimpleDB{
		Pool:   nil, // We'll use the GORM DB for now
		Config: cfg,
	}

	// Create regular auth handlers for Google OAuth
	regularAuthHandlers, err := handlers.NewAuthHandlers(cfg, simpleDB)
	if err != nil {
		logger.Error("Failed to create regular auth handlers", zap.Error(err))
		return
	}

	// Public routes group
	public := v1.Group("/public")

	// Configure input validation for public routes
	inputValidationConfig := &middleware.InputValidationConfig{
		MaxRequestSize:               1 * 1024 * 1024, // 1MB for public users
		MaxStringLength:              500,
		MaxArrayLength:               50,
		EnableXSSProtection:          true,
		EnableSQLInjectionProtection: true,
		Logger:                       logger,
	}
	public.Use(middleware.InputValidationMiddleware(inputValidationConfig))

	// Configure strict rate limiting for public routes
	rateLimitConfig := &middleware.RateLimitConfig{
		RequestsPerMinute: 30, // Strict for public
		RequestsPerHour:   500,
		RequestsPerDay:    2000,
		BlockDuration:     middleware.DefaultRateLimitConfig().BlockDuration,
		CleanupInterval:   middleware.DefaultRateLimitConfig().CleanupInterval,
		WhitelistedIPs:    []string{"127.0.0.1", "::1"},
		Logger:            logger,
	}
	public.Use(middleware.RateLimitMiddleware(rateLimitConfig))

	// Public routes
	{
		public.POST("/auth/google", regularAuthHandlers.GoogleOAuth)
		public.POST("/auth/login", regularAuthHandlers.Login)
		public.POST("/auth/refresh", regularAuthHandlers.RefreshToken)
		public.GET("/health", func(c *gin.Context) {
			c.JSON(200, gin.H{"status": "healthy", "service": "carnow-backend"})
		})
	}

	// Public product routes (read-only)
	products := v1.Group("/products")
	{
		// Configure input validation for product routes
		products.Use(middleware.InputValidationMiddleware(inputValidationConfig))
		products.Use(middleware.RateLimitMiddleware(rateLimitConfig))

		// Initialize product handlers
		sqlDB, _ := db.GetDB().DB()
		productRepo := productrepositories.NewProductRepository(sqlDB)
		productService := productservices.NewProductService(productRepo, logger)
		productHandler := producthandlers.NewProductHandler(productService, logger)

		// Public product routes
		products.GET("/", productHandler.GetAllProducts)
		products.GET("/categories", productHandler.GetProductCategories)
		products.GET("/featured", productHandler.GetFeaturedProducts)
		products.GET("/search", productHandler.SearchProducts)
		products.GET("/:id", productHandler.GetProductByID)
		products.GET("/category/:category_id", productHandler.GetProductsByCategory)
		products.GET("/:id/recommendations", productHandler.GetProductRecommendations)
	}

	// Public categories routes
	categories := v1.Group("/categories")
	{
		// Configure input validation for category routes
		categories.Use(middleware.InputValidationMiddleware(inputValidationConfig))
		categories.Use(middleware.RateLimitMiddleware(rateLimitConfig))

		// Initialize product handlers (categories are handled by product service)
		sqlDB, _ := db.GetDB().DB()
		productRepo := productrepositories.NewProductRepository(sqlDB)
		productService := productservices.NewProductService(productRepo, logger)
		productHandler := producthandlers.NewProductHandler(productService, logger)

		// Public category routes
		categories.GET("/", productHandler.GetProductCategories)
	}
}

// setupAuthenticatedRoutes configures authenticated API routes with auth middleware
func setupAuthenticatedRoutes(v1 *gin.RouterGroup, cfg *config.Config, db *database.Database, logger *zap.Logger) {
	// Create a database adapter that implements DatabaseInterface
	dbAdapter := &databaseAdapter{db: db}

	// Initialize JWT service for enhanced authentication (Task 5: Update authentication handlers)
	jwtService, err := services.NewJWTService(cfg)
	if err != nil {
		logger.Error("Failed to initialize JWT service for authenticated routes", zap.Error(err))
		// Fallback to simple JWT middleware if enhanced service fails
		logger.Warn("Falling back to simple JWT middleware")
	}

	// Authenticated routes group
	auth := v1.Group("/auth")

	// Configure input validation for authenticated routes
	inputValidationConfig := &middleware.InputValidationConfig{
		MaxRequestSize:               2 * 1024 * 1024, // 2MB for authenticated users
		MaxStringLength:              1000,
		MaxArrayLength:               100,
		EnableXSSProtection:          true,
		EnableSQLInjectionProtection: true,
		Logger:                       logger,
	}
	auth.Use(middleware.InputValidationMiddleware(inputValidationConfig))

	// Configure more lenient rate limiting for authenticated users
	rateLimitConfig := &middleware.RateLimitConfig{
		RequestsPerMinute: 100, // More lenient for authenticated
		RequestsPerHour:   2000,
		RequestsPerDay:    10000,
		BlockDuration:     middleware.DefaultRateLimitConfig().BlockDuration,
		CleanupInterval:   middleware.DefaultRateLimitConfig().CleanupInterval,
		WhitelistedIPs:    []string{"127.0.0.1", "::1"},
		Logger:            logger,
	}
	auth.Use(middleware.RateLimitMiddleware(rateLimitConfig))

	// Enhanced JWT authentication middleware (Task 5: Update authentication handlers)
	if jwtService != nil {
		auth.Use(enhancedmiddleware.EnhancedJWTMiddleware(cfg, jwtService))
	} else {
		// Fallback to simple JWT middleware
		auth.Use(middleware.SimpleJWTMiddleware(cfg, dbAdapter))
	}

	// Create a SimpleDB adapter for handlers that expect it
	simpleDB := &database.SimpleDB{
		Pool:   nil, // We'll use the GORM DB for now
		Config: cfg,
	}

	// Create authenticated handlers
	authHandlers, _ := handlers.NewSecureAuthHandlers(cfg, simpleDB)

	// Authenticated routes
	{
		auth.POST("/logout", authHandlers.SecureLogout)
		auth.POST("/change-password", authHandlers.ChangePassword)
	}

	// User routes
	users := auth.Group("/users")
	{
		users.GET("/profile", getUserProfile)
		users.PUT("/profile", updateUserProfile)
		users.GET("/preferences", getUserPreferences)
		users.PUT("/preferences", updateUserPreferences)
	}

	// Product routes (authenticated)
	products := auth.Group("/products")
	{
		// Initialize product handlers
		sqlDB, _ := db.GetDB().DB()
		productRepo := productrepositories.NewProductRepository(sqlDB)
		productService := productservices.NewProductService(productRepo, logger)
		productHandler := producthandlers.NewProductHandler(productService, logger)

		// Authenticated product routes
		products.POST("/", productHandler.CreateProduct)
		products.PUT("/:id", productHandler.UpdateProduct)
		products.DELETE("/:id", productHandler.DeleteProduct)
	}

	// Wallet routes - using simple handlers for now
	wallet := auth.Group("/wallet")
	{
		// Simple wallet endpoints that the Flutter UI expects
		wallet.GET("/", getWalletHandler)
		wallet.GET("/transactions", getWalletTransactionsHandler)
	}

	// Order routes
	orders := auth.Group("/orders")
	{
		// Initialize order handlers
		sqlDB, _ := db.GetDB().DB()
		orderRepo := orderrepositories.NewOrderRepository(sqlDB)
		orderService := orderservices.NewOrderService(orderRepo, logger)
		orderHandler := orderhandlers.NewOrderHandler(orderService, logger)

		// Order routes
		orders.GET("/", orderHandler.GetAllOrders)
		orders.GET("/active", orderHandler.GetActiveOrders)
		orders.GET("/:id", orderHandler.GetOrderByID)
		orders.POST("/", orderHandler.CreateOrder)
		orders.PUT("/:id/status", orderHandler.UpdateOrderStatus)
		orders.DELETE("/:id", orderHandler.CancelOrder)
		orders.GET("/statistics", orderHandler.GetOrderStatistics)
		orders.GET("/:id/tracking", orderHandler.TrackOrder)
	}

}

// setupAdminRoutes configures admin-only routes with highest security
func setupAdminRoutes(v1 *gin.RouterGroup, cfg *config.Config, db *database.Database, logger *zap.Logger) {
	// Create a database adapter that implements DatabaseInterface
	dbAdapter := &databaseAdapter{db: db}

	// Admin routes group
	admin := v1.Group("/admin")

	// Configure strict input validation for admin routes
	inputValidationConfig := &middleware.InputValidationConfig{
		MaxRequestSize:               5 * 1024 * 1024, // 5MB for admin operations
		MaxStringLength:              2000,
		MaxArrayLength:               200,
		EnableXSSProtection:          true,
		EnableSQLInjectionProtection: true,
		Logger:                       logger,
	}
	admin.Use(middleware.InputValidationMiddleware(inputValidationConfig))

	// Configure admin rate limiting
	rateLimitConfig := &middleware.RateLimitConfig{
		RequestsPerMinute: 200, // Higher limit for admin
		RequestsPerHour:   5000,
		RequestsPerDay:    50000,
		BlockDuration:     middleware.DefaultRateLimitConfig().BlockDuration,
		CleanupInterval:   middleware.DefaultRateLimitConfig().CleanupInterval,
		WhitelistedIPs:    []string{"127.0.0.1", "::1"},
		Logger:            logger,
	}
	admin.Use(middleware.RateLimitMiddleware(rateLimitConfig))

	// JWT authentication middleware
	admin.Use(middleware.SimpleJWTMiddleware(cfg, dbAdapter))

	// TODO: Add admin role middleware here
	// admin.Use(middleware.AdminRoleMiddleware())

	// Financial admin routes
	financial := admin.Group("/financial")
	{
		// Initialize admin financial handlers
		sqlDB, _ := db.GetDB().DB()
		adminRepo := adminrepositories.NewAdminRepository(sqlDB)
		adminService := adminservices.NewAdminFinancialService(adminRepo, logger)
		adminHandler := adminhandlers.NewAdminFinancialHandler(adminService, logger)

		// Admin financial routes
		financial.GET("/dashboard", adminHandler.GetFinancialDashboard)
		financial.GET("/wallets", adminHandler.GetWalletManagement)
		financial.GET("/alerts", adminHandler.GetFinancialAlerts)
		financial.GET("/actions", adminHandler.GetRecentActions)
		financial.GET("/health", adminHandler.GetSystemHealth)
		financial.PUT("/alerts/:id", adminHandler.UpdateAlertStatus)
		financial.GET("/reports", adminHandler.GetFinancialReports)
	}

	// User management routes
	users := admin.Group("/users")
	{
		users.GET("/", getAllUsers)
		users.GET("/:id", getUser)
		users.PUT("/:id", updateUser)
		users.DELETE("/:id", deleteUser)
		users.POST("/:id/ban", banUser)
		users.POST("/:id/unban", unbanUser)
	}

	// System management routes
	system := admin.Group("/system")
	{
		system.GET("/stats", getSystemStats)
		system.GET("/health", getDetailedHealth)
		system.GET("/logs", getSystemLogs)
		system.GET("/audit", getAuditLogs)
		system.GET("/security", getSecurityThreats)
		system.POST("/rate-limit/reset", resetRateLimit)
		system.POST("/ip/block", blockIP)
		system.POST("/ip/unblock", unblockIP)
	}

	// Database management routes
	database := admin.Group("/database")
	{
		database.GET("/health", getDatabaseHealth)
		database.POST("/password/rotate", rotateDatabasePassword)
		database.GET("/audit/stats", getDatabaseAuditStats)
		database.POST("/audit/cleanup", cleanupDatabaseAudit)
	}
}

// databaseAdapter adapts the GORM Database to implement DatabaseInterface
type databaseAdapter struct {
	db *database.Database
}

// QueryRow implements DatabaseInterface.QueryRow
func (da *databaseAdapter) QueryRow(ctx context.Context, query string, args ...interface{}) pgx.Row {
	// For now, return a mock row since we're using GORM
	// In production, you'd want to use the underlying pgx connection
	return nil
}

// Exec implements DatabaseInterface.Exec
func (da *databaseAdapter) Exec(ctx context.Context, query string, args ...interface{}) error {
	// Use GORM's Exec method
	result := da.db.Exec(query, args...)
	return result.Error
}

// =============================================================================
// PLACEHOLDER FUNCTIONS FOR REMAINING ROUTES
// =============================================================================

func getUserProfile(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Get user profile"})
}

func updateUserProfile(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Update user profile"})
}

func getUserPreferences(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Get user preferences"})
}

func updateUserPreferences(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Update user preferences"})
}

func getAllUsers(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Get all users"})
}

func getUser(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Get user"})
}

func updateUser(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Update user"})
}

func deleteUser(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Delete user"})
}

func banUser(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Ban user"})
}

func unbanUser(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Unban user"})
}

func getSystemStats(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Get system stats"})
}

func getDetailedHealth(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Get detailed health"})
}

func getSystemLogs(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Get system logs"})
}

func getAuditLogs(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Get audit logs"})
}

func getSecurityThreats(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Get security threats"})
}

func resetRateLimit(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Reset rate limit"})
}

func blockIP(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Block IP"})
}

func unblockIP(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Unblock IP"})
}

func getDatabaseHealth(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Get database health"})
}

func rotateDatabasePassword(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Rotate database password"})
}

func getDatabaseAuditStats(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Get database audit stats"})
}

func cleanupDatabaseAudit(c *gin.Context) {
	c.JSON(200, gin.H{"message": "Cleanup database audit"})
}

func getCities(c *gin.Context) {
	// Temporary stub implementation that returns static cities.
	// TODO: Wire this endpoint to the real handler once database layer is ready.
	cities := []gin.H{
		{"id": "1", "name": "Riyadh", "country": "Saudi Arabia"},
		{"id": "2", "name": "Jeddah", "country": "Saudi Arabia"},
		{"id": "3", "name": "Dammam", "country": "Saudi Arabia"},
		{"id": "4", "name": "Mecca", "country": "Saudi Arabia"},
		{"id": "5", "name": "Medina", "country": "Saudi Arabia"},
	}
	c.JSON(200, gin.H{"cities": cities})
}

// =============================================================================
// SIMPLE HANDLERS FOR FLUTTER UI INTEGRATION
// =============================================================================

// Simple wallet handler for Flutter UI
func getWalletHandler(c *gin.Context) {
	userID := middleware.GetUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Simple mock response for now
	c.JSON(http.StatusOK, gin.H{
		"wallet": gin.H{
			"id":          "wallet-123",
			"user_id":     userID,
			"balance":     1000.0,
			"currency":    "SAR",
			"status":      "active",
			"is_verified": true,
			"created_at":  time.Now().Format(time.RFC3339),
			"updated_at":  time.Now().Format(time.RFC3339),
		},
		"success": true,
	})
}

// Simple wallet transactions handler for Flutter UI
func getWalletTransactionsHandler(c *gin.Context) {
	userID := middleware.GetUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Simple mock response for now
	c.JSON(http.StatusOK, gin.H{
		"transactions": []gin.H{
			{
				"id":          "txn-1",
				"wallet_id":   "wallet-123",
				"amount":      100.0,
				"type":        "deposit",
				"status":      "completed",
				"description": "Initial deposit",
				"created_at":  time.Now().Add(-24 * time.Hour).Format(time.RFC3339),
			},
			{
				"id":          "txn-2",
				"wallet_id":   "wallet-123",
				"amount":      50.0,
				"type":        "withdrawal",
				"status":      "completed",
				"description": "Payment for order",
				"created_at":  time.Now().Add(-12 * time.Hour).Format(time.RFC3339),
			},
		},
		"success": true,
	})
}
