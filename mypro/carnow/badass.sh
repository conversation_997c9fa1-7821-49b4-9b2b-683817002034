#!/bin/bash

# 🔥 CarNow Badass Local Server Script 🔥
# تشغيل السيرفر المحلي بطريقة احترافية

set -e  # Exit on any error

# ==================== CONFIGURATION ====================
BACKEND_DIR="backend-go"
BINARY_NAME="carnow-backend"
PORT="8080"
LOCAL_URL="http://********:8080"
ENV_FILE=".env"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# ==================== FUNCTIONS ====================

print_banner() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    🔥 CARNOW BADASS 🔥                       ║"
    echo "║                  Local Server Launcher                      ║"
    echo "║                                                              ║"
    echo "║  🚀 Starting Go Backend on http://********:8080            ║"
    echo "║  🎯 Forever Plan Architecture Compliant                     ║"
    echo "║  ⚡ Production-Ready Local Development                       ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_step() {
    echo -e "${CYAN}🔧 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if we're in the right directory
check_directory() {
    if [[ ! -f "$ENV_FILE" ]]; then
        print_error "Not in CarNow root directory! Please run from mypro/carnow/"
        exit 1
    fi
    
    if [[ ! -d "$BACKEND_DIR" ]]; then
        print_error "Backend directory not found! Expected: $BACKEND_DIR"
        exit 1
    fi
}

# Kill any existing process on port 8080
kill_existing_server() {
    print_step "Checking for existing servers on port $PORT..."
    
    if lsof -ti:$PORT >/dev/null 2>&1; then
        print_warning "Found existing process on port $PORT. Killing it..."
        lsof -ti:$PORT | xargs kill -9 2>/dev/null || true
        sleep 2
        print_success "Existing server killed"
    else
        print_info "No existing server found on port $PORT"
    fi
}

# Update .env to use local server
update_env_file() {
    print_step "Updating .env file to use local server..."
    
    # Backup original .env
    cp "$ENV_FILE" "${ENV_FILE}.backup"
    
    # Update API_BASE_URL to local
    if grep -q "API_BASE_URL=" "$ENV_FILE"; then
        sed -i.tmp "s|API_BASE_URL=.*|API_BASE_URL=$LOCAL_URL|g" "$ENV_FILE"
        rm -f "${ENV_FILE}.tmp"
        print_success "Updated API_BASE_URL to $LOCAL_URL"
    else
        echo "API_BASE_URL=$LOCAL_URL" >> "$ENV_FILE"
        print_success "Added API_BASE_URL=$LOCAL_URL to .env"
    fi
    
    print_info "Original .env backed up as .env.backup"
}

# Build the Go backend
build_backend() {
    print_step "Building Go backend..."
    
    cd "$BACKEND_DIR"
    
    # Clean previous builds
    rm -f "$BINARY_NAME"
    go clean -cache
    
    # Build the backend
    if go build -o "$BINARY_NAME" ./cmd/main.go; then
        print_success "Backend built successfully"
    else
        print_error "Failed to build backend"
        exit 1
    fi
    
    cd ..
}

# Start the backend server
start_backend() {
    print_step "Starting Go backend server..."
    
    cd "$BACKEND_DIR"
    
    # Start the server in background
    nohup ./"$BINARY_NAME" > ../backend.log 2>&1 &
    BACKEND_PID=$!
    
    # Save PID for later cleanup
    echo $BACKEND_PID > ../backend.pid
    
    cd ..
    
    print_success "Backend started with PID: $BACKEND_PID"
    print_info "Logs are being written to backend.log"
}

# Wait for server to be ready
wait_for_server() {
    print_step "Waiting for server to be ready..."

    local max_attempts=30
    local attempt=1

    # Try localhost first (server actually runs on localhost)
    local health_url="http://localhost:8080/health"

    while [[ $attempt -le $max_attempts ]]; do
        if curl -s "$health_url" >/dev/null 2>&1; then
            print_success "Server is ready and responding!"
            return 0
        fi

        echo -n "."
        sleep 1
        ((attempt++))
    done

    print_error "Server failed to start within 30 seconds"
    return 1
}

# Show server status
show_status() {
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    🎉 SERVER READY! 🎉                      ║"
    echo "║                                                              ║"
    echo "║  🌐 Backend URL: $LOCAL_URL                     ║"
    echo "║  📊 Health Check: $LOCAL_URL/health             ║"
    echo "║  📱 Flutter App: Ready to connect                           ║"
    echo "║  📝 Logs: backend.log                                       ║"
    echo "║  🔧 PID File: backend.pid                                    ║"
    echo "║                                                              ║"
    echo "║  🚀 Now run your Flutter app!                               ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Show useful commands
show_commands() {
    echo -e "${YELLOW}"
    echo "📋 Useful Commands:"
    echo "   • View logs: tail -f backend.log"
    echo "   • Stop server: kill \$(cat backend.pid)"
    echo "   • Health check: curl $LOCAL_URL/health"
    echo "   • Restart: ./badass.sh"
    echo -e "${NC}"
}

# Cleanup function for graceful shutdown
cleanup() {
    print_warning "Shutting down..."
    if [[ -f backend.pid ]]; then
        local pid=$(cat backend.pid)
        if kill -0 "$pid" 2>/dev/null; then
            kill "$pid"
            print_success "Backend server stopped"
        fi
        rm -f backend.pid
    fi
}

# ==================== MAIN EXECUTION ====================

# Set up signal handlers for graceful shutdown
trap cleanup EXIT INT TERM

# Main execution
main() {
    print_banner
    
    check_directory
    kill_existing_server
    update_env_file
    build_backend
    start_backend
    
    if wait_for_server; then
        show_status
        show_commands
        
        print_info "Press Ctrl+C to stop the server"
        
        # Keep script running to maintain server
        while true; do
            if [[ -f backend.pid ]]; then
                local pid=$(cat backend.pid)
                if ! kill -0 "$pid" 2>/dev/null; then
                    print_error "Backend server died unexpectedly!"
                    break
                fi
            else
                print_error "PID file missing!"
                break
            fi
            sleep 5
        done
    else
        print_error "Failed to start server"
        exit 1
    fi
}

# Run main function
main "$@"
