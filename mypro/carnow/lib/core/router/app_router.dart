import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../features/account/screens/complete_profile_required_screen.dart';
import '../../features/account/screens/login_required_screen.dart';
import '../../navigation/widgets/main_layout.dart';
import '../../navigation/models/navigation_route.dart';
import '../auth/auth_provider_initializer.dart';
import '../auth/auth_models.dart';
import 'routes/account_routes.dart';
import 'routes/cart_routes.dart';
import 'routes/chat_routes.dart';
import 'routes/developer_routes.dart';
import 'routes/seller_routes.dart';
// import 'routes/wallet_routes.dart'; // Temporarily disabled
import 'routes/missing_routes.dart';
import '../../navigation/providers/navigation_route_provider.dart';
import '../../features/recommendations/routes/recommendation_routes.dart';
import '../../features/garage/routes/garage_routes.dart';
import '../../features/favorites/routes/favorites_routes.dart';
import '../../features/products/listings/screens/specialized/specialized_product_details_factory.dart';
import '../../features/browse/routes/browse_routes.dart';
import '../../features/products/listings/routes/subcategory_routes.dart';
import '../providers/admin_provider.dart';
import '../../features/admin_tools/vehicles/screens/screens.dart'
    as admin_vehicles;
import '../../features/admin_tools/users/screens/screens.dart' as admin_users;
import '../../features/admin_tools/screens/admin_financial_dashboard_screen.dart';
import '../../features/admin_tools/screens/admin_wallet_management_screen.dart';
import '../../features/wallet/screens/modern_wallet_screen.dart';
import '../../features/settings/screens/settings_screen.dart';
import '../../examples/settings_test_screen.dart';

final _rootNavigatorKey = GlobalKey<NavigatorState>();

final appRouterProvider = Provider<GoRouter>((ref) {
  // Safely access providers with fallbacks to prevent initialization race conditions
  AuthState authState;
  try {
    authState = ref.watch(safeAuthStateProvider);
  } catch (e) {
    if (kDebugMode) {
      print('AppRouter: Safe auth state provider not ready, using initial state: $e');
    }
    authState = const AuthState.initial();
  }
  
  List<NavigationRoute> navigationRoutes;
  try {
    navigationRoutes = ref.watch(navigationRouteProvider);
  } catch (e) {
    if (kDebugMode) {
      print('AppRouter: Navigation route provider not ready, using empty list: $e');
    }
    navigationRoutes = [];
  }
  
  // Safely determine if the current user has admin privileges
  bool isAdmin = false;
  try {
    isAdmin = ref.watch(isAdminProvider);
  } catch (e) {
    // If admin provider is not ready, default to false
    if (kDebugMode) {
      print('AppRouter: Admin provider not ready, defaulting to false: $e');
    }
    isAdmin = false;
  }
  
  // Debug: Log router initialization
  if (kDebugMode) {
    print('AppRouter: Initializing with ${navigationRoutes.length} navigation routes');
    print('AppRouter: Auth state is ${authState.runtimeType}');
    print('AppRouter: Admin status is $isAdmin');
  }

  // A helper function to check if a route is protected.
  // تم تعديل المنطق لجعل المسارات متاحة مع رسائل تحذيرية عند الحاجة
  bool isProtectedRoute(String path) {
    // إزالة الحماية من أغلب المسارات للسماح بالتصفح بدون تسجيل
    const strictlyProtectedPaths = [
      '/checkout', // فقط صفحة الشراء تحتاج تسجيل دخول
      '/seller', // إدارة البائعين تحتاج تسجيل دخول
      '/account/profile/edit', // تعديل الملف الشخصي
      '/change-password', // تغيير كلمة المرور
    ];
    return strictlyProtectedPaths.any((p) => path.startsWith(p));
  }

  // A helper function to check if the user is currently on an auth-related page.
  bool isAuthRoute(String path) {
    const authPaths = [
      '/login',
      '/register',
      '/login-required',
      '/complete-profile-required',
    ];
    return authPaths.contains(path);
  }

  return GoRouter(
    navigatorKey: _rootNavigatorKey,
    initialLocation: '/',
    refreshListenable: ValueNotifier(
      authState,
    ), // Re-evaluates on auth state change
    errorBuilder: (context, state) {
      if (kDebugMode) {
        print('GoRouter Error: ${state.error}');
        print('GoRouter Error Location: ${state.matchedLocation}');
      }
      return Scaffold(
        appBar: AppBar(
          title: const Text('خطأ في التنقل'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                'حدث خطأ في التنقل',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                'المسار المطلوب: ${state.matchedLocation}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => context.go('/'),
                child: const Text('العودة للصفحة الرئيسية'),
              ),
            ],
          ),
        ),
      );
    },
    redirect: (context, state) {
      final path = state.matchedLocation;

      // Handle authentication state with unified auth system
      switch (authState) {
        case AuthStateInitial():
        case AuthStateLoading():
          // While loading, don't redirect. A splash screen or loading indicator should be shown.
          return null;

        case AuthStateUnauthenticated():
          // السماح للمستخدم بالتصفح بدون تسجيل - إعادة التوجيه فقط للمسارات المحمية جداً
          if (isProtectedRoute(path)) {
            // Pass the original destination to redirect back after login.
            return '/login-required?from=${state.uri.toString()}';
          }
          // السماح بالوصول لجميع المسارات الأخرى بدون تسجيل دخول
          break;

        case AuthStateAuthenticated(user: final user):
          // Check if user has complete profile
          if (!user.hasCompleteProfile) {
            // If a logged-in user with an incomplete profile tries to checkout,
            // redirect them to complete their profile first.
            if (path == '/checkout') {
              return '/complete-profile-required?from=${state.uri.toString()}';
            }
          }
          
          // If authenticated user lands on an auth page, redirect to home
          if (isAuthRoute(path)) {
            final from = state.uri.queryParameters['from'] ?? '/';
            return from;
          }
          break;

        case AuthStateError():
          // Handle error case, maybe show an error page or allow access
          // depending on the desired behavior. For now, no redirect.
          break;

        case AuthStateEmailVerificationPending():
          // Allow access but may show verification prompt in UI
          break;

        case AuthStateSessionExpired():
          // Redirect to login for session expired
          if (isProtectedRoute(path)) {
            return '/login-required?from=${state.uri.toString()}';
          }
          break;
      }

      // Restrict developer routes to admin users only (even in debug mode).
      if ((state.matchedLocation.startsWith('/dev') ||
              state.matchedLocation == '/developer-tools' ||
              state.matchedLocation.startsWith('/admin/financial') ||
              state.matchedLocation.startsWith('/admin/wallet-management')) &&
          !isAdmin) {
        return '/';
      }

      // For all other cases, no redirect is needed.
      return null;
    },
    routes: [
      // Only add StatefulShellRoute if navigation routes are available
      if (navigationRoutes.isNotEmpty)
        StatefulShellRoute.indexedStack(
          builder: (context, state, navigationShell) =>
              MainLayout(navigationShell: navigationShell),
          branches: navigationRoutes
              .map((navRoute) => StatefulShellBranch(routes: [navRoute.route]))
              .toList(),
        ),

      // Standalone interstitial routes
      GoRoute(
        path: '/login-required',
        builder: (context, state) =>
            LoginRequiredScreen(from: state.uri.queryParameters['from']),
      ),
      GoRoute(
        path: '/complete-profile-required',
        builder: (context, state) => CompleteProfileRequiredScreen(
          from: state.uri.queryParameters['from'],
        ),
      ),

      // Add all modularized routes
      ...accountRoutes,
      
      // Settings route
      GoRoute(
        path: '/settings',
        builder: (context, state) => const SettingsScreen(),
      ),
      
      // Debug settings test route
      GoRoute(
        path: '/debug/settings-test',
        builder: (context, state) => const SettingsTestScreen(),
      ),
      
      ...sellerRoutes,
      ...chatRoutes,
      ...cartRoutes,
      
      // Wallet route - FIXED: Added missing wallet route
      GoRoute(
        path: '/wallet',
        builder: (context, state) => const ModernWalletScreen(),
      ),
      ...garageRoutes,
      ...RecommendationRoutes.routes,
      ...FavoritesRoutes.routes,
      ...BrowseRoutes.routes,
      ...missingRoutes,
      // Add developer routes only in debug mode *and* when the authenticated
      // user is an admin. This prevents non-admin testers from accessing
      // sensitive developer tooling even in debug builds.
      if (kDebugMode && isAdmin) ...developerRoutes,
      if (isAdmin) ...[
        GoRoute(
          path: '/admin/financial-dashboard',
          pageBuilder: (context, state) => MaterialPage(
            key: state.pageKey,
            child: const AdminFinancialDashboardScreen(),
          ),
        ),
        GoRoute(
          path: '/admin/wallet-management',
          pageBuilder: (context, state) => MaterialPage(
            key: state.pageKey,
            child: const AdminWalletManagementScreen(),
          ),
        ),
      ],
      GoRoute(
        path: '/product-details/:productId',
        name: 'product-details',
        builder: (context, state) {
          final productId = state.pathParameters['productId']!;
          return ProductDetailsWrapper(productId: productId);
        },
      ),
      SubcategoryRoutes.route,
      // Developer Tools Routes
      GoRoute(
        path: '/developer/additions/add-car',
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const admin_vehicles.AddCarScreen(),
        ),
      ),
      // Admin User Management Routes
      GoRoute(
        path: '/admin/users',
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const admin_users.AdminUsersListScreen(),
        ),
      ),
      GoRoute(
        path: '/admin/manage-admins',
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const admin_users.AdminManagementScreen(),
        ),
      ),
    ],
  );
});
