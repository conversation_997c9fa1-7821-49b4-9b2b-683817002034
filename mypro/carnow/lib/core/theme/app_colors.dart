import 'package:flutter/material.dart';

/// Canonical application color palette.
/// Use this file across the entire code-base instead of defining duplicate
/// `AppColors` classes in multiple locations.
///
/// If you need custom feature-specific colors, prefer adding an extension or
/// a namespaced class rather than redefining `AppColors`.
class AppColors {
  // =================== Brand Palette v2 ======================
  // Inspired by eBay for an energetic car marketplace
  // 1. Primary  — eBay Red (energy & urgency)
  // 2. Secondary— eBay Yellow (highlights & optimism)
  // 3. Tertiary — eBay Green    (success & compatibility)
  // ==========================================================

  /*
   *  Primary — eBay Red
   *  – evokes energy, speed, and excitement (cars, auctions)
   *  – WCAG AA compliant
   */
  static const primary = Color(0xFFE53238); // eBay Red
  static const primaryLight = Color(0xFFFF6F74); // +40 lightness
  static const primaryDark = Color(0xFFB00005); // −25 lightness

  // Brand colors for garage feature (Forever Plan Architecture)
  static const primaryBrand = Color(0xFF1B5E20); // Forest Green - CarNow Brand

  /*
   *  Secondary — eBay Yellow
   *  – for CTAs and highlights
   */
  static const secondary = Color(0xFFF5D400);
  static const secondaryLight = Color(0xFFFFE74C);
  static const secondaryDark = Color(0xFFC2A200);

  /*
   *  Tertiary — eBay Green
   *  – for success states and eco-friendly features
   */
  static const tertiary = Color(0xFF86B817);
  static const tertiaryLight = Color(0xFFBDE954);
  static const tertiaryDark = Color(0xFF598900);

  // Neutral greys
  static const white = Colors.white;
  static const black = Color(
    0xFF1A1A1A,
  ); // Not pure black for better aesthetics
  static const grey = Color(0xFF8A94A6); // Cool Grey
  static const greyLight = Color(0xFFF8F9FA); // Off-white
  static const greyDark = Color(0xFF5A5A5A);

  // Status
  static const success = Color(0xFF86B817); // Now using tertiary green
  static const warning = Color(0xFFF5D400); // Now using secondary yellow
  static const error = Color(0xFFE53238); // Now using primary red
  static const info = Color(0xFF0064D2); // Keeping eBay blue for info

  // Background & surfaces
  static const background = Color(0xFFF8F9FA);
  static const backgroundLight = Color(0xFFFCFDFE);
  static const surface = Colors.white;
  static const card = Colors.white;

  // Borders
  static const border = Color(0xFFE1E4E8);
  static const borderLight = Color(0xFFEFF1F3);
  static const borderDark = Color(0xFFCED3D9);

  // Auction-specific (aligned with the new palette)
  static const auctionActive = Color(0xFF86B817); // tertiary green
  static const auctionEnding = Color(0xFFF5D400); // secondary yellow
  static const auctionEnded = Color(0xFF8A94A6); // grey
  static const bidWinning = Color(0xFFE53238); // primary red
  static const bidLosing = Color(0xFFC2A200); // secondary dark

  /// Map auction status string → color helper
  static Color auctionStatus(String status) => switch (status.toLowerCase()) {
    'active' => auctionActive,
    'ending' => auctionEnding,
    'ended' => auctionEnded,
    _ => grey,
  };

  /// Map bid outcome → color helper
  static Color bidStatus({required bool isWinning}) =>
      isWinning ? bidWinning : bidLosing;

  // Text colors
  static const textPrimary = Color(0xFF1A1A1A); // High-emphasis text
  static const textSecondary = Color(0xFF5A5A5A); // Medium-emphasis text
  static const textHint = Color(0xFF8A94A6); // Hints / placeholders
  static const textOnPrimary =
      Colors.white; // Text on primary colored backgrounds

  // Commerce-specific colors
  /// Price and discount values
  static const price = Color(0xFFE53238); // primary red for attention

  static const metallic = Color(0xFF78909C);
  static const premium = Color(0xFF37474F);
  static const performance = Color(0xFFD32F2F);
  static const efficiency = Color(0xFF388E3C);

  static const successContainer = Color(0xFFC8E6C9);
  static const warningContainer = Color(0xFFFFE0B2);
  static const errorContainer = Color(0xFFFFCDD2);
}
