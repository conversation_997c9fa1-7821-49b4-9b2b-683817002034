# 🚗 Garage Feature - Forever Plan Architecture

## Overview

The Garage feature allows users to manage their vehicles in the CarNow application. It follows the Forever Plan Architecture principles with a clean separation between UI (Flutter), business logic (Go API), and data storage (Supabase).

## Architecture

```
Flutter (UI Only) → Go API (Business Logic) → Supabase (Data Storage)
```

### Key Principles
- **Flutter**: UI components only, no business logic
- **Go Backend**: All business logic, validation, and database operations
- **Supabase**: Data storage and authentication only
- **Real Data Only**: Zero tolerance for mock or hardcoded data
- **Material 3**: Complete design system compliance

## Features

### 🚗 Vehicle Management
- Add new vehicles to garage
- Edit existing vehicle information
- Delete vehicles from garage
- View detailed vehicle information

### 🔍 Vehicle Selection System
- Hierarchical vehicle selection (Make → Model → Year → Trim → Engine)
- Smart filtering and search capabilities
- Real-time data from Supabase database

### 📱 User Interface
- Material 3 design system
- Responsive design for all screen sizes
- Accessibility compliant (WCAG 2.1 AAA)
- Arabic language support

## File Structure

```
lib/features/garage/
├── models/                 # Data models (Freezed)
│   ├── garage_models.dart
│   ├── user_vehicle.dart
│   ├── vehicle_make.dart
│   ├── vehicle_model.dart
│   ├── vehicle_year.dart
│   ├── vehicle_trim.dart
│   └── vehicle_engine.dart
├── providers/              # Riverpod providers
│   └── garage_providers.dart
├── repositories/           # Data repositories
│   └── garage_repository.dart
├── services/              # Business services
│   └── garage_service.dart
├── screens/               # UI screens
│   ├── garage_screen.dart
│   ├── add_vehicle_screen.dart
│   ├── edit_vehicle_screen.dart
│   └── vehicle_details_screen.dart
├── widgets/               # Reusable widgets
│   ├── vehicle_card.dart
│   ├── vehicle_selection_stepper.dart
│   ├── vehicle_selection_dropdown.dart
│   └── vehicle_details_form.dart
├── routes/                # Navigation routes
│   └── garage_routes.dart
├── navigation/            # Navigation utilities
│   └── garage_navigation.dart
└── README.md             # This file
```

## Navigation

### Routes
- `/garage` - Main garage screen
- `/garage/add` - Add new vehicle
- `/garage/vehicle/:id` - Vehicle details
- `/garage/vehicle/:id/edit` - Edit vehicle

### Navigation Utilities
```dart
import 'package:carnow/features/garage/navigation/garage_navigation.dart';

// Navigate to garage
context.goToGarage();

// Navigate to add vehicle
context.goToAddVehicle();

// Navigate to vehicle details
context.goToVehicleDetails('vehicle-id');

// Safe navigation with error handling
await context.safeGoToGarage();
```

## Data Models

### UserVehicle
```dart
@freezed
class UserVehicle with _$UserVehicle {
  const factory UserVehicle({
    required int id,
    required String userId,
    required int makeId,
    required int modelId,
    required int yearId,
    int? trimId,
    int? engineId,
    String? color,
    String? licensePlate,
    String? vin,
    int? mileage,
    @Default(false) bool isPrimary,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _UserVehicle;
}
```

### Vehicle Reference Data
- **VehicleMake**: Car manufacturers (Toyota, BMW, etc.)
- **VehicleModel**: Car models (Camry, X5, etc.)
- **VehicleYear**: Production years
- **VehicleTrim**: Trim levels (Base, Sport, Luxury)
- **VehicleEngine**: Engine specifications

## API Integration

### Go Backend Endpoints
```
GET    /api/v1/garage/makes              # Get vehicle makes
GET    /api/v1/garage/models/:make_id    # Get models for make
GET    /api/v1/garage/years/:model_id    # Get years for model
GET    /api/v1/garage/trims/:model_id    # Get trims for model
GET    /api/v1/garage/engines/:model_id  # Get engines for model

GET    /api/v1/garage/my-vehicles        # Get user vehicles
POST   /api/v1/garage/vehicles           # Create vehicle
PUT    /api/v1/garage/vehicles/:id       # Update vehicle
DELETE /api/v1/garage/vehicles/:id       # Delete vehicle
```

### Authentication
All API calls require JWT authentication. The token is automatically included by the SimpleApiClient.

## State Management

### Riverpod Providers
```dart
// Vehicle reference data
final vehicleMakesProvider = FutureProvider.family<List<VehicleMake>, void>(...);
final vehicleModelsProvider = FutureProvider.family<List<VehicleModel>, int>(...);

// User vehicles
final userVehiclesProvider = FutureProvider<List<UserVehicle>>(...);

// Vehicle selection state
final vehicleSelectionStateProvider = StateNotifierProvider<VehicleSelectionNotifier, VehicleSelectionState>(...);
```

## Testing

### Unit Tests
```bash
flutter test test/features/garage/
```

### Integration Tests
```bash
flutter test integration_test/garage_test.dart
```

## Development Guidelines

### Forever Plan Compliance
1. **No Mock Data**: All data must come from real Supabase database
2. **UI Only**: Flutter contains only UI components
3. **Business Logic**: All logic in Go backend
4. **Material 3**: Use design system components only
5. **Real Data**: Zero tolerance for hardcoded statistics

### Code Quality
- 95%+ test coverage required
- All models use Freezed for immutability
- Riverpod for state management
- GoRouter for navigation
- Material 3 design system

### Performance
- Lazy loading for large lists
- Caching for frequently accessed data
- Optimized images and assets
- Responsive design for all devices

## Troubleshooting

### Common Issues
1. **Build Errors**: Run `flutter pub run build_runner build --delete-conflicting-outputs`
2. **Navigation Issues**: Check route definitions in `garage_routes.dart`
3. **API Errors**: Verify Go backend is running and accessible
4. **State Issues**: Check Riverpod provider dependencies

### Debug Mode
Enable debug logging in development:
```dart
if (kDebugMode) {
  print('Garage Debug: $message');
}
```

## Contributing

1. Follow Forever Plan Architecture principles
2. Maintain 95%+ test coverage
3. Use Material 3 design system
4. Ensure accessibility compliance
5. Document all changes
6. Use real data only (no mock data)

## License

This code is part of the CarNow application and follows the project's licensing terms.
