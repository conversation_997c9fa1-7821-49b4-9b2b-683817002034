import 'package:freezed_annotation/freezed_annotation.dart';

part 'simple_vehicle.freezed.dart';
part 'simple_vehicle.g.dart';

/// Simple Vehicle Model - Forever Plan Architecture Compliant
/// Lightweight model for compatibility and quick references
@freezed
abstract class SimpleVehicle with _$SimpleVehicle {
  const factory SimpleVehicle({
    required String id,
    required String makeName,
    required String modelName,
    required int year,
    String? trim,
    String? engine,
    String? fuelType,
    String? bodyType,
    
    // Reference IDs for detailed lookups
    int? makeRefId,
    int? modelRefId,
    int? yearRefId,
    int? trimRefId,
    int? engineRefId,
  }) = _SimpleVehicle;

  factory SimpleVehicle.fromJson(Map<String, dynamic> json) =>
      _$SimpleVehicleFromJson(json);
  
  /// Create SimpleVehicle from UserVehicle
  factory SimpleVehicle.fromUserVehicle(Map<String, dynamic> userVehicle) {
    return SimpleVehicle(
      id: userVehicle['id']?.toString() ?? '',
      makeName: userVehicle['make'] ?? userVehicle['makeName'] ?? '',
      modelName: userVehicle['model'] ?? userVehicle['modelName'] ?? '',
      year: userVehicle['year'] ?? 0,
      trim: userVehicle['trim'],
      engine: userVehicle['engine'],
      fuelType: userVehicle['fuel_type'] ?? userVehicle['fuelType'],
      bodyType: userVehicle['body_style'] ?? userVehicle['bodyType'],
      makeRefId: userVehicle['make_ref_id'] ?? userVehicle['makeRefId'],
      modelRefId: userVehicle['model_ref_id'] ?? userVehicle['modelRefId'],
      yearRefId: userVehicle['year_ref_id'] ?? userVehicle['yearRefId'],
      trimRefId: userVehicle['trim_ref_id'] ?? userVehicle['trimRefId'],
      engineRefId: userVehicle['engine_ref_id'] ?? userVehicle['engineRefId'],
    );
  }
}

/// Simple Vehicle List Response
@freezed
abstract class SimpleVehicleListResponse with _$SimpleVehicleListResponse {
  const factory SimpleVehicleListResponse({
    required List<SimpleVehicle> vehicles,
    required int total,
    @Default(false) bool hasMore,
  }) = _SimpleVehicleListResponse;

  factory SimpleVehicleListResponse.fromJson(Map<String, dynamic> json) =>
      _$SimpleVehicleListResponseFromJson(json);
}
