// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'simple_vehicle.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SimpleVehicle {

 String get id; String get makeName; String get modelName; int get year; String? get trim; String? get engine; String? get fuelType; String? get bodyType;// Reference IDs for detailed lookups
 int? get makeRefId; int? get modelRefId; int? get yearRefId; int? get trimRefId; int? get engineRefId;
/// Create a copy of SimpleVehicle
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SimpleVehicleCopyWith<SimpleVehicle> get copyWith => _$SimpleVehicleCopyWithImpl<SimpleVehicle>(this as SimpleVehicle, _$identity);

  /// Serializes this SimpleVehicle to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SimpleVehicle&&(identical(other.id, id) || other.id == id)&&(identical(other.makeName, makeName) || other.makeName == makeName)&&(identical(other.modelName, modelName) || other.modelName == modelName)&&(identical(other.year, year) || other.year == year)&&(identical(other.trim, trim) || other.trim == trim)&&(identical(other.engine, engine) || other.engine == engine)&&(identical(other.fuelType, fuelType) || other.fuelType == fuelType)&&(identical(other.bodyType, bodyType) || other.bodyType == bodyType)&&(identical(other.makeRefId, makeRefId) || other.makeRefId == makeRefId)&&(identical(other.modelRefId, modelRefId) || other.modelRefId == modelRefId)&&(identical(other.yearRefId, yearRefId) || other.yearRefId == yearRefId)&&(identical(other.trimRefId, trimRefId) || other.trimRefId == trimRefId)&&(identical(other.engineRefId, engineRefId) || other.engineRefId == engineRefId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,makeName,modelName,year,trim,engine,fuelType,bodyType,makeRefId,modelRefId,yearRefId,trimRefId,engineRefId);

@override
String toString() {
  return 'SimpleVehicle(id: $id, makeName: $makeName, modelName: $modelName, year: $year, trim: $trim, engine: $engine, fuelType: $fuelType, bodyType: $bodyType, makeRefId: $makeRefId, modelRefId: $modelRefId, yearRefId: $yearRefId, trimRefId: $trimRefId, engineRefId: $engineRefId)';
}


}

/// @nodoc
abstract mixin class $SimpleVehicleCopyWith<$Res>  {
  factory $SimpleVehicleCopyWith(SimpleVehicle value, $Res Function(SimpleVehicle) _then) = _$SimpleVehicleCopyWithImpl;
@useResult
$Res call({
 String id, String makeName, String modelName, int year, String? trim, String? engine, String? fuelType, String? bodyType, int? makeRefId, int? modelRefId, int? yearRefId, int? trimRefId, int? engineRefId
});




}
/// @nodoc
class _$SimpleVehicleCopyWithImpl<$Res>
    implements $SimpleVehicleCopyWith<$Res> {
  _$SimpleVehicleCopyWithImpl(this._self, this._then);

  final SimpleVehicle _self;
  final $Res Function(SimpleVehicle) _then;

/// Create a copy of SimpleVehicle
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? makeName = null,Object? modelName = null,Object? year = null,Object? trim = freezed,Object? engine = freezed,Object? fuelType = freezed,Object? bodyType = freezed,Object? makeRefId = freezed,Object? modelRefId = freezed,Object? yearRefId = freezed,Object? trimRefId = freezed,Object? engineRefId = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,makeName: null == makeName ? _self.makeName : makeName // ignore: cast_nullable_to_non_nullable
as String,modelName: null == modelName ? _self.modelName : modelName // ignore: cast_nullable_to_non_nullable
as String,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,trim: freezed == trim ? _self.trim : trim // ignore: cast_nullable_to_non_nullable
as String?,engine: freezed == engine ? _self.engine : engine // ignore: cast_nullable_to_non_nullable
as String?,fuelType: freezed == fuelType ? _self.fuelType : fuelType // ignore: cast_nullable_to_non_nullable
as String?,bodyType: freezed == bodyType ? _self.bodyType : bodyType // ignore: cast_nullable_to_non_nullable
as String?,makeRefId: freezed == makeRefId ? _self.makeRefId : makeRefId // ignore: cast_nullable_to_non_nullable
as int?,modelRefId: freezed == modelRefId ? _self.modelRefId : modelRefId // ignore: cast_nullable_to_non_nullable
as int?,yearRefId: freezed == yearRefId ? _self.yearRefId : yearRefId // ignore: cast_nullable_to_non_nullable
as int?,trimRefId: freezed == trimRefId ? _self.trimRefId : trimRefId // ignore: cast_nullable_to_non_nullable
as int?,engineRefId: freezed == engineRefId ? _self.engineRefId : engineRefId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// Adds pattern-matching-related methods to [SimpleVehicle].
extension SimpleVehiclePatterns on SimpleVehicle {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SimpleVehicle value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SimpleVehicle() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SimpleVehicle value)  $default,){
final _that = this;
switch (_that) {
case _SimpleVehicle():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SimpleVehicle value)?  $default,){
final _that = this;
switch (_that) {
case _SimpleVehicle() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String makeName,  String modelName,  int year,  String? trim,  String? engine,  String? fuelType,  String? bodyType,  int? makeRefId,  int? modelRefId,  int? yearRefId,  int? trimRefId,  int? engineRefId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SimpleVehicle() when $default != null:
return $default(_that.id,_that.makeName,_that.modelName,_that.year,_that.trim,_that.engine,_that.fuelType,_that.bodyType,_that.makeRefId,_that.modelRefId,_that.yearRefId,_that.trimRefId,_that.engineRefId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String makeName,  String modelName,  int year,  String? trim,  String? engine,  String? fuelType,  String? bodyType,  int? makeRefId,  int? modelRefId,  int? yearRefId,  int? trimRefId,  int? engineRefId)  $default,) {final _that = this;
switch (_that) {
case _SimpleVehicle():
return $default(_that.id,_that.makeName,_that.modelName,_that.year,_that.trim,_that.engine,_that.fuelType,_that.bodyType,_that.makeRefId,_that.modelRefId,_that.yearRefId,_that.trimRefId,_that.engineRefId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String makeName,  String modelName,  int year,  String? trim,  String? engine,  String? fuelType,  String? bodyType,  int? makeRefId,  int? modelRefId,  int? yearRefId,  int? trimRefId,  int? engineRefId)?  $default,) {final _that = this;
switch (_that) {
case _SimpleVehicle() when $default != null:
return $default(_that.id,_that.makeName,_that.modelName,_that.year,_that.trim,_that.engine,_that.fuelType,_that.bodyType,_that.makeRefId,_that.modelRefId,_that.yearRefId,_that.trimRefId,_that.engineRefId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SimpleVehicle implements SimpleVehicle {
  const _SimpleVehicle({required this.id, required this.makeName, required this.modelName, required this.year, this.trim, this.engine, this.fuelType, this.bodyType, this.makeRefId, this.modelRefId, this.yearRefId, this.trimRefId, this.engineRefId});
  factory _SimpleVehicle.fromJson(Map<String, dynamic> json) => _$SimpleVehicleFromJson(json);

@override final  String id;
@override final  String makeName;
@override final  String modelName;
@override final  int year;
@override final  String? trim;
@override final  String? engine;
@override final  String? fuelType;
@override final  String? bodyType;
// Reference IDs for detailed lookups
@override final  int? makeRefId;
@override final  int? modelRefId;
@override final  int? yearRefId;
@override final  int? trimRefId;
@override final  int? engineRefId;

/// Create a copy of SimpleVehicle
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SimpleVehicleCopyWith<_SimpleVehicle> get copyWith => __$SimpleVehicleCopyWithImpl<_SimpleVehicle>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SimpleVehicleToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SimpleVehicle&&(identical(other.id, id) || other.id == id)&&(identical(other.makeName, makeName) || other.makeName == makeName)&&(identical(other.modelName, modelName) || other.modelName == modelName)&&(identical(other.year, year) || other.year == year)&&(identical(other.trim, trim) || other.trim == trim)&&(identical(other.engine, engine) || other.engine == engine)&&(identical(other.fuelType, fuelType) || other.fuelType == fuelType)&&(identical(other.bodyType, bodyType) || other.bodyType == bodyType)&&(identical(other.makeRefId, makeRefId) || other.makeRefId == makeRefId)&&(identical(other.modelRefId, modelRefId) || other.modelRefId == modelRefId)&&(identical(other.yearRefId, yearRefId) || other.yearRefId == yearRefId)&&(identical(other.trimRefId, trimRefId) || other.trimRefId == trimRefId)&&(identical(other.engineRefId, engineRefId) || other.engineRefId == engineRefId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,makeName,modelName,year,trim,engine,fuelType,bodyType,makeRefId,modelRefId,yearRefId,trimRefId,engineRefId);

@override
String toString() {
  return 'SimpleVehicle(id: $id, makeName: $makeName, modelName: $modelName, year: $year, trim: $trim, engine: $engine, fuelType: $fuelType, bodyType: $bodyType, makeRefId: $makeRefId, modelRefId: $modelRefId, yearRefId: $yearRefId, trimRefId: $trimRefId, engineRefId: $engineRefId)';
}


}

/// @nodoc
abstract mixin class _$SimpleVehicleCopyWith<$Res> implements $SimpleVehicleCopyWith<$Res> {
  factory _$SimpleVehicleCopyWith(_SimpleVehicle value, $Res Function(_SimpleVehicle) _then) = __$SimpleVehicleCopyWithImpl;
@override @useResult
$Res call({
 String id, String makeName, String modelName, int year, String? trim, String? engine, String? fuelType, String? bodyType, int? makeRefId, int? modelRefId, int? yearRefId, int? trimRefId, int? engineRefId
});




}
/// @nodoc
class __$SimpleVehicleCopyWithImpl<$Res>
    implements _$SimpleVehicleCopyWith<$Res> {
  __$SimpleVehicleCopyWithImpl(this._self, this._then);

  final _SimpleVehicle _self;
  final $Res Function(_SimpleVehicle) _then;

/// Create a copy of SimpleVehicle
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? makeName = null,Object? modelName = null,Object? year = null,Object? trim = freezed,Object? engine = freezed,Object? fuelType = freezed,Object? bodyType = freezed,Object? makeRefId = freezed,Object? modelRefId = freezed,Object? yearRefId = freezed,Object? trimRefId = freezed,Object? engineRefId = freezed,}) {
  return _then(_SimpleVehicle(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,makeName: null == makeName ? _self.makeName : makeName // ignore: cast_nullable_to_non_nullable
as String,modelName: null == modelName ? _self.modelName : modelName // ignore: cast_nullable_to_non_nullable
as String,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,trim: freezed == trim ? _self.trim : trim // ignore: cast_nullable_to_non_nullable
as String?,engine: freezed == engine ? _self.engine : engine // ignore: cast_nullable_to_non_nullable
as String?,fuelType: freezed == fuelType ? _self.fuelType : fuelType // ignore: cast_nullable_to_non_nullable
as String?,bodyType: freezed == bodyType ? _self.bodyType : bodyType // ignore: cast_nullable_to_non_nullable
as String?,makeRefId: freezed == makeRefId ? _self.makeRefId : makeRefId // ignore: cast_nullable_to_non_nullable
as int?,modelRefId: freezed == modelRefId ? _self.modelRefId : modelRefId // ignore: cast_nullable_to_non_nullable
as int?,yearRefId: freezed == yearRefId ? _self.yearRefId : yearRefId // ignore: cast_nullable_to_non_nullable
as int?,trimRefId: freezed == trimRefId ? _self.trimRefId : trimRefId // ignore: cast_nullable_to_non_nullable
as int?,engineRefId: freezed == engineRefId ? _self.engineRefId : engineRefId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}


/// @nodoc
mixin _$SimpleVehicleListResponse {

 List<SimpleVehicle> get vehicles; int get total; bool get hasMore;
/// Create a copy of SimpleVehicleListResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SimpleVehicleListResponseCopyWith<SimpleVehicleListResponse> get copyWith => _$SimpleVehicleListResponseCopyWithImpl<SimpleVehicleListResponse>(this as SimpleVehicleListResponse, _$identity);

  /// Serializes this SimpleVehicleListResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SimpleVehicleListResponse&&const DeepCollectionEquality().equals(other.vehicles, vehicles)&&(identical(other.total, total) || other.total == total)&&(identical(other.hasMore, hasMore) || other.hasMore == hasMore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(vehicles),total,hasMore);

@override
String toString() {
  return 'SimpleVehicleListResponse(vehicles: $vehicles, total: $total, hasMore: $hasMore)';
}


}

/// @nodoc
abstract mixin class $SimpleVehicleListResponseCopyWith<$Res>  {
  factory $SimpleVehicleListResponseCopyWith(SimpleVehicleListResponse value, $Res Function(SimpleVehicleListResponse) _then) = _$SimpleVehicleListResponseCopyWithImpl;
@useResult
$Res call({
 List<SimpleVehicle> vehicles, int total, bool hasMore
});




}
/// @nodoc
class _$SimpleVehicleListResponseCopyWithImpl<$Res>
    implements $SimpleVehicleListResponseCopyWith<$Res> {
  _$SimpleVehicleListResponseCopyWithImpl(this._self, this._then);

  final SimpleVehicleListResponse _self;
  final $Res Function(SimpleVehicleListResponse) _then;

/// Create a copy of SimpleVehicleListResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? vehicles = null,Object? total = null,Object? hasMore = null,}) {
  return _then(_self.copyWith(
vehicles: null == vehicles ? _self.vehicles : vehicles // ignore: cast_nullable_to_non_nullable
as List<SimpleVehicle>,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int,hasMore: null == hasMore ? _self.hasMore : hasMore // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [SimpleVehicleListResponse].
extension SimpleVehicleListResponsePatterns on SimpleVehicleListResponse {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SimpleVehicleListResponse value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SimpleVehicleListResponse() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SimpleVehicleListResponse value)  $default,){
final _that = this;
switch (_that) {
case _SimpleVehicleListResponse():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SimpleVehicleListResponse value)?  $default,){
final _that = this;
switch (_that) {
case _SimpleVehicleListResponse() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<SimpleVehicle> vehicles,  int total,  bool hasMore)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SimpleVehicleListResponse() when $default != null:
return $default(_that.vehicles,_that.total,_that.hasMore);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<SimpleVehicle> vehicles,  int total,  bool hasMore)  $default,) {final _that = this;
switch (_that) {
case _SimpleVehicleListResponse():
return $default(_that.vehicles,_that.total,_that.hasMore);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<SimpleVehicle> vehicles,  int total,  bool hasMore)?  $default,) {final _that = this;
switch (_that) {
case _SimpleVehicleListResponse() when $default != null:
return $default(_that.vehicles,_that.total,_that.hasMore);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SimpleVehicleListResponse implements SimpleVehicleListResponse {
  const _SimpleVehicleListResponse({required final  List<SimpleVehicle> vehicles, required this.total, this.hasMore = false}): _vehicles = vehicles;
  factory _SimpleVehicleListResponse.fromJson(Map<String, dynamic> json) => _$SimpleVehicleListResponseFromJson(json);

 final  List<SimpleVehicle> _vehicles;
@override List<SimpleVehicle> get vehicles {
  if (_vehicles is EqualUnmodifiableListView) return _vehicles;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_vehicles);
}

@override final  int total;
@override@JsonKey() final  bool hasMore;

/// Create a copy of SimpleVehicleListResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SimpleVehicleListResponseCopyWith<_SimpleVehicleListResponse> get copyWith => __$SimpleVehicleListResponseCopyWithImpl<_SimpleVehicleListResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SimpleVehicleListResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SimpleVehicleListResponse&&const DeepCollectionEquality().equals(other._vehicles, _vehicles)&&(identical(other.total, total) || other.total == total)&&(identical(other.hasMore, hasMore) || other.hasMore == hasMore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_vehicles),total,hasMore);

@override
String toString() {
  return 'SimpleVehicleListResponse(vehicles: $vehicles, total: $total, hasMore: $hasMore)';
}


}

/// @nodoc
abstract mixin class _$SimpleVehicleListResponseCopyWith<$Res> implements $SimpleVehicleListResponseCopyWith<$Res> {
  factory _$SimpleVehicleListResponseCopyWith(_SimpleVehicleListResponse value, $Res Function(_SimpleVehicleListResponse) _then) = __$SimpleVehicleListResponseCopyWithImpl;
@override @useResult
$Res call({
 List<SimpleVehicle> vehicles, int total, bool hasMore
});




}
/// @nodoc
class __$SimpleVehicleListResponseCopyWithImpl<$Res>
    implements _$SimpleVehicleListResponseCopyWith<$Res> {
  __$SimpleVehicleListResponseCopyWithImpl(this._self, this._then);

  final _SimpleVehicleListResponse _self;
  final $Res Function(_SimpleVehicleListResponse) _then;

/// Create a copy of SimpleVehicleListResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? vehicles = null,Object? total = null,Object? hasMore = null,}) {
  return _then(_SimpleVehicleListResponse(
vehicles: null == vehicles ? _self._vehicles : vehicles // ignore: cast_nullable_to_non_nullable
as List<SimpleVehicle>,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int,hasMore: null == hasMore ? _self.hasMore : hasMore // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
