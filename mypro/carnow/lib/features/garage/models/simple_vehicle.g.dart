// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'simple_vehicle.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SimpleVehicle _$SimpleVehicleFromJson(Map<String, dynamic> json) =>
    _SimpleVehicle(
      id: json['id'] as String,
      makeName: json['makeName'] as String,
      modelName: json['modelName'] as String,
      year: (json['year'] as num).toInt(),
      trim: json['trim'] as String?,
      engine: json['engine'] as String?,
      fuelType: json['fuelType'] as String?,
      bodyType: json['bodyType'] as String?,
      makeRefId: (json['makeRefId'] as num?)?.toInt(),
      modelRefId: (json['modelRefId'] as num?)?.toInt(),
      yearRefId: (json['yearRefId'] as num?)?.toInt(),
      trimRefId: (json['trimRefId'] as num?)?.toInt(),
      engineRefId: (json['engineRefId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$SimpleVehicleToJson(_SimpleVehicle instance) =>
    <String, dynamic>{
      'id': instance.id,
      'makeName': instance.makeName,
      'modelName': instance.modelName,
      'year': instance.year,
      'trim': instance.trim,
      'engine': instance.engine,
      'fuelType': instance.fuelType,
      'bodyType': instance.bodyType,
      'makeRefId': instance.makeRefId,
      'modelRefId': instance.modelRefId,
      'yearRefId': instance.yearRefId,
      'trimRefId': instance.trimRefId,
      'engineRefId': instance.engineRefId,
    };

_SimpleVehicleListResponse _$SimpleVehicleListResponseFromJson(
  Map<String, dynamic> json,
) => _SimpleVehicleListResponse(
  vehicles: (json['vehicles'] as List<dynamic>)
      .map((e) => SimpleVehicle.fromJson(e as Map<String, dynamic>))
      .toList(),
  total: (json['total'] as num).toInt(),
  hasMore: json['hasMore'] as bool? ?? false,
);

Map<String, dynamic> _$SimpleVehicleListResponseToJson(
  _SimpleVehicleListResponse instance,
) => <String, dynamic>{
  'vehicles': instance.vehicles,
  'total': instance.total,
  'hasMore': instance.hasMore,
};
