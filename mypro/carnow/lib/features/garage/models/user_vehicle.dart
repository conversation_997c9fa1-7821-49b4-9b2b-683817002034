import 'package:freezed_annotation/freezed_annotation.dart';
import 'vehicle_make.dart';
import 'vehicle_model.dart';
import 'vehicle_year.dart';
import 'vehicle_trim.dart';
import 'vehicle_engine.dart';

part 'user_vehicle.freezed.dart';
part 'user_vehicle.g.dart';

/// User Vehicle - Forever Plan Architecture Compliant
/// Represents a user's vehicle in their garage
@freezed
abstract class UserVehicle with _$UserVehicle {
  const factory UserVehicle({
    required int id,
    required String userId,
    required String make,
    required String model,
    required int year,
    String? trim,
    String? engine,
    String? color,
    String? vin,
    String? licensePlate,
    @Default(0) int mileage,
    @Default('good') String condition,
    DateTime? purchaseDate,
    double? purchasePrice,
    DateTime? insuranceExpiry,
    DateTime? lastServiceDate,
    DateTime? nextServiceDue,
    @Default([]) List<String> imageUrls,
    @Default(false) bool isPrimary,
    @Default(true) bool isActive,
    required DateTime createdAt,
    required DateTime updatedAt,
    
    // Enhanced reference data
    VehicleMake? makeRef,
    VehicleModel? modelRef,
    VehicleYear? yearRef,
    VehicleTrim? trimRef,
    VehicleEngine? engineRef,
  }) = _UserVehicle;

  factory UserVehicle.fromJson(Map<String, dynamic> json) =>
      _$UserVehicleFromJson(json);
}

/// User Vehicle List Response
@freezed
abstract class UserVehicleListResponse with _$UserVehicleListResponse {
  const factory UserVehicleListResponse({
    required List<UserVehicle> vehicles,
    required int total,
    @Default(false) bool hasMore,
  }) = _UserVehicleListResponse;

  factory UserVehicleListResponse.fromJson(Map<String, dynamic> json) =>
      _$UserVehicleListResponseFromJson(json);
}

/// Create User Vehicle Request
@freezed
abstract class CreateUserVehicleRequest with _$CreateUserVehicleRequest {
  const factory CreateUserVehicleRequest({
    required int makeRefId,
    required int modelRefId,
    int? yearRefId,
    int? trimRefId,
    int? engineRefId,
    String? color,
    String? vin,
    String? licensePlate,
    @Default(0) int mileage,
    String? notes,
    DateTime? purchaseDate,
    double? purchasePrice,
    DateTime? insuranceExpiry,
    DateTime? lastServiceDate,
    DateTime? nextServiceDue,
    @Default('good') String condition,
    @Default([]) List<String> imageUrls,
    @Default(false) bool isPrimary,
  }) = _CreateUserVehicleRequest;

  factory CreateUserVehicleRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateUserVehicleRequestFromJson(json);
}

/// Update User Vehicle Request
@freezed
abstract class UpdateUserVehicleRequest with _$UpdateUserVehicleRequest {
  const factory UpdateUserVehicleRequest({
    String? color,
    String? vin,
    String? licensePlate,
    int? mileage,
    String? notes,
    DateTime? purchaseDate,
    double? purchasePrice,
    DateTime? insuranceExpiry,
    DateTime? lastServiceDate,
    DateTime? nextServiceDue,
    String? condition,
    List<String>? imageUrls,
    bool? isPrimary,
    bool? isActive,
  }) = _UpdateUserVehicleRequest;

  factory UpdateUserVehicleRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateUserVehicleRequestFromJson(json);
}
