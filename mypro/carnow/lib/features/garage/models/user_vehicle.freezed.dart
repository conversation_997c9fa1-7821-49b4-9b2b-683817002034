// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_vehicle.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserVehicle {

 int get id; String get userId; String get make; String get model; int get year; String? get trim; String? get engine; String? get color; String? get vin; String? get licensePlate; int get mileage; String get condition; DateTime? get purchaseDate; double? get purchasePrice; DateTime? get insuranceExpiry; DateTime? get lastServiceDate; DateTime? get nextServiceDue; List<String> get imageUrls; bool get isPrimary; bool get isActive; DateTime get createdAt; DateTime get updatedAt;// Enhanced reference data
 VehicleMake? get makeRef; VehicleModel? get modelRef; VehicleYear? get yearRef; VehicleTrim? get trimRef; VehicleEngine? get engineRef;
/// Create a copy of UserVehicle
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserVehicleCopyWith<UserVehicle> get copyWith => _$UserVehicleCopyWithImpl<UserVehicle>(this as UserVehicle, _$identity);

  /// Serializes this UserVehicle to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserVehicle&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.make, make) || other.make == make)&&(identical(other.model, model) || other.model == model)&&(identical(other.year, year) || other.year == year)&&(identical(other.trim, trim) || other.trim == trim)&&(identical(other.engine, engine) || other.engine == engine)&&(identical(other.color, color) || other.color == color)&&(identical(other.vin, vin) || other.vin == vin)&&(identical(other.licensePlate, licensePlate) || other.licensePlate == licensePlate)&&(identical(other.mileage, mileage) || other.mileage == mileage)&&(identical(other.condition, condition) || other.condition == condition)&&(identical(other.purchaseDate, purchaseDate) || other.purchaseDate == purchaseDate)&&(identical(other.purchasePrice, purchasePrice) || other.purchasePrice == purchasePrice)&&(identical(other.insuranceExpiry, insuranceExpiry) || other.insuranceExpiry == insuranceExpiry)&&(identical(other.lastServiceDate, lastServiceDate) || other.lastServiceDate == lastServiceDate)&&(identical(other.nextServiceDue, nextServiceDue) || other.nextServiceDue == nextServiceDue)&&const DeepCollectionEquality().equals(other.imageUrls, imageUrls)&&(identical(other.isPrimary, isPrimary) || other.isPrimary == isPrimary)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.makeRef, makeRef) || other.makeRef == makeRef)&&(identical(other.modelRef, modelRef) || other.modelRef == modelRef)&&(identical(other.yearRef, yearRef) || other.yearRef == yearRef)&&(identical(other.trimRef, trimRef) || other.trimRef == trimRef)&&(identical(other.engineRef, engineRef) || other.engineRef == engineRef));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,make,model,year,trim,engine,color,vin,licensePlate,mileage,condition,purchaseDate,purchasePrice,insuranceExpiry,lastServiceDate,nextServiceDue,const DeepCollectionEquality().hash(imageUrls),isPrimary,isActive,createdAt,updatedAt,makeRef,modelRef,yearRef,trimRef,engineRef]);

@override
String toString() {
  return 'UserVehicle(id: $id, userId: $userId, make: $make, model: $model, year: $year, trim: $trim, engine: $engine, color: $color, vin: $vin, licensePlate: $licensePlate, mileage: $mileage, condition: $condition, purchaseDate: $purchaseDate, purchasePrice: $purchasePrice, insuranceExpiry: $insuranceExpiry, lastServiceDate: $lastServiceDate, nextServiceDue: $nextServiceDue, imageUrls: $imageUrls, isPrimary: $isPrimary, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, makeRef: $makeRef, modelRef: $modelRef, yearRef: $yearRef, trimRef: $trimRef, engineRef: $engineRef)';
}


}

/// @nodoc
abstract mixin class $UserVehicleCopyWith<$Res>  {
  factory $UserVehicleCopyWith(UserVehicle value, $Res Function(UserVehicle) _then) = _$UserVehicleCopyWithImpl;
@useResult
$Res call({
 int id, String userId, String make, String model, int year, String? trim, String? engine, String? color, String? vin, String? licensePlate, int mileage, String condition, DateTime? purchaseDate, double? purchasePrice, DateTime? insuranceExpiry, DateTime? lastServiceDate, DateTime? nextServiceDue, List<String> imageUrls, bool isPrimary, bool isActive, DateTime createdAt, DateTime updatedAt, VehicleMake? makeRef, VehicleModel? modelRef, VehicleYear? yearRef, VehicleTrim? trimRef, VehicleEngine? engineRef
});


$VehicleMakeCopyWith<$Res>? get makeRef;$VehicleModelCopyWith<$Res>? get modelRef;$VehicleYearCopyWith<$Res>? get yearRef;$VehicleTrimCopyWith<$Res>? get trimRef;$VehicleEngineCopyWith<$Res>? get engineRef;

}
/// @nodoc
class _$UserVehicleCopyWithImpl<$Res>
    implements $UserVehicleCopyWith<$Res> {
  _$UserVehicleCopyWithImpl(this._self, this._then);

  final UserVehicle _self;
  final $Res Function(UserVehicle) _then;

/// Create a copy of UserVehicle
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? make = null,Object? model = null,Object? year = null,Object? trim = freezed,Object? engine = freezed,Object? color = freezed,Object? vin = freezed,Object? licensePlate = freezed,Object? mileage = null,Object? condition = null,Object? purchaseDate = freezed,Object? purchasePrice = freezed,Object? insuranceExpiry = freezed,Object? lastServiceDate = freezed,Object? nextServiceDue = freezed,Object? imageUrls = null,Object? isPrimary = null,Object? isActive = null,Object? createdAt = null,Object? updatedAt = null,Object? makeRef = freezed,Object? modelRef = freezed,Object? yearRef = freezed,Object? trimRef = freezed,Object? engineRef = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,make: null == make ? _self.make : make // ignore: cast_nullable_to_non_nullable
as String,model: null == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,trim: freezed == trim ? _self.trim : trim // ignore: cast_nullable_to_non_nullable
as String?,engine: freezed == engine ? _self.engine : engine // ignore: cast_nullable_to_non_nullable
as String?,color: freezed == color ? _self.color : color // ignore: cast_nullable_to_non_nullable
as String?,vin: freezed == vin ? _self.vin : vin // ignore: cast_nullable_to_non_nullable
as String?,licensePlate: freezed == licensePlate ? _self.licensePlate : licensePlate // ignore: cast_nullable_to_non_nullable
as String?,mileage: null == mileage ? _self.mileage : mileage // ignore: cast_nullable_to_non_nullable
as int,condition: null == condition ? _self.condition : condition // ignore: cast_nullable_to_non_nullable
as String,purchaseDate: freezed == purchaseDate ? _self.purchaseDate : purchaseDate // ignore: cast_nullable_to_non_nullable
as DateTime?,purchasePrice: freezed == purchasePrice ? _self.purchasePrice : purchasePrice // ignore: cast_nullable_to_non_nullable
as double?,insuranceExpiry: freezed == insuranceExpiry ? _self.insuranceExpiry : insuranceExpiry // ignore: cast_nullable_to_non_nullable
as DateTime?,lastServiceDate: freezed == lastServiceDate ? _self.lastServiceDate : lastServiceDate // ignore: cast_nullable_to_non_nullable
as DateTime?,nextServiceDue: freezed == nextServiceDue ? _self.nextServiceDue : nextServiceDue // ignore: cast_nullable_to_non_nullable
as DateTime?,imageUrls: null == imageUrls ? _self.imageUrls : imageUrls // ignore: cast_nullable_to_non_nullable
as List<String>,isPrimary: null == isPrimary ? _self.isPrimary : isPrimary // ignore: cast_nullable_to_non_nullable
as bool,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,makeRef: freezed == makeRef ? _self.makeRef : makeRef // ignore: cast_nullable_to_non_nullable
as VehicleMake?,modelRef: freezed == modelRef ? _self.modelRef : modelRef // ignore: cast_nullable_to_non_nullable
as VehicleModel?,yearRef: freezed == yearRef ? _self.yearRef : yearRef // ignore: cast_nullable_to_non_nullable
as VehicleYear?,trimRef: freezed == trimRef ? _self.trimRef : trimRef // ignore: cast_nullable_to_non_nullable
as VehicleTrim?,engineRef: freezed == engineRef ? _self.engineRef : engineRef // ignore: cast_nullable_to_non_nullable
as VehicleEngine?,
  ));
}
/// Create a copy of UserVehicle
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VehicleMakeCopyWith<$Res>? get makeRef {
    if (_self.makeRef == null) {
    return null;
  }

  return $VehicleMakeCopyWith<$Res>(_self.makeRef!, (value) {
    return _then(_self.copyWith(makeRef: value));
  });
}/// Create a copy of UserVehicle
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VehicleModelCopyWith<$Res>? get modelRef {
    if (_self.modelRef == null) {
    return null;
  }

  return $VehicleModelCopyWith<$Res>(_self.modelRef!, (value) {
    return _then(_self.copyWith(modelRef: value));
  });
}/// Create a copy of UserVehicle
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VehicleYearCopyWith<$Res>? get yearRef {
    if (_self.yearRef == null) {
    return null;
  }

  return $VehicleYearCopyWith<$Res>(_self.yearRef!, (value) {
    return _then(_self.copyWith(yearRef: value));
  });
}/// Create a copy of UserVehicle
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VehicleTrimCopyWith<$Res>? get trimRef {
    if (_self.trimRef == null) {
    return null;
  }

  return $VehicleTrimCopyWith<$Res>(_self.trimRef!, (value) {
    return _then(_self.copyWith(trimRef: value));
  });
}/// Create a copy of UserVehicle
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VehicleEngineCopyWith<$Res>? get engineRef {
    if (_self.engineRef == null) {
    return null;
  }

  return $VehicleEngineCopyWith<$Res>(_self.engineRef!, (value) {
    return _then(_self.copyWith(engineRef: value));
  });
}
}


/// Adds pattern-matching-related methods to [UserVehicle].
extension UserVehiclePatterns on UserVehicle {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UserVehicle value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UserVehicle() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UserVehicle value)  $default,){
final _that = this;
switch (_that) {
case _UserVehicle():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UserVehicle value)?  $default,){
final _that = this;
switch (_that) {
case _UserVehicle() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String userId,  String make,  String model,  int year,  String? trim,  String? engine,  String? color,  String? vin,  String? licensePlate,  int mileage,  String condition,  DateTime? purchaseDate,  double? purchasePrice,  DateTime? insuranceExpiry,  DateTime? lastServiceDate,  DateTime? nextServiceDue,  List<String> imageUrls,  bool isPrimary,  bool isActive,  DateTime createdAt,  DateTime updatedAt,  VehicleMake? makeRef,  VehicleModel? modelRef,  VehicleYear? yearRef,  VehicleTrim? trimRef,  VehicleEngine? engineRef)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UserVehicle() when $default != null:
return $default(_that.id,_that.userId,_that.make,_that.model,_that.year,_that.trim,_that.engine,_that.color,_that.vin,_that.licensePlate,_that.mileage,_that.condition,_that.purchaseDate,_that.purchasePrice,_that.insuranceExpiry,_that.lastServiceDate,_that.nextServiceDue,_that.imageUrls,_that.isPrimary,_that.isActive,_that.createdAt,_that.updatedAt,_that.makeRef,_that.modelRef,_that.yearRef,_that.trimRef,_that.engineRef);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String userId,  String make,  String model,  int year,  String? trim,  String? engine,  String? color,  String? vin,  String? licensePlate,  int mileage,  String condition,  DateTime? purchaseDate,  double? purchasePrice,  DateTime? insuranceExpiry,  DateTime? lastServiceDate,  DateTime? nextServiceDue,  List<String> imageUrls,  bool isPrimary,  bool isActive,  DateTime createdAt,  DateTime updatedAt,  VehicleMake? makeRef,  VehicleModel? modelRef,  VehicleYear? yearRef,  VehicleTrim? trimRef,  VehicleEngine? engineRef)  $default,) {final _that = this;
switch (_that) {
case _UserVehicle():
return $default(_that.id,_that.userId,_that.make,_that.model,_that.year,_that.trim,_that.engine,_that.color,_that.vin,_that.licensePlate,_that.mileage,_that.condition,_that.purchaseDate,_that.purchasePrice,_that.insuranceExpiry,_that.lastServiceDate,_that.nextServiceDue,_that.imageUrls,_that.isPrimary,_that.isActive,_that.createdAt,_that.updatedAt,_that.makeRef,_that.modelRef,_that.yearRef,_that.trimRef,_that.engineRef);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String userId,  String make,  String model,  int year,  String? trim,  String? engine,  String? color,  String? vin,  String? licensePlate,  int mileage,  String condition,  DateTime? purchaseDate,  double? purchasePrice,  DateTime? insuranceExpiry,  DateTime? lastServiceDate,  DateTime? nextServiceDue,  List<String> imageUrls,  bool isPrimary,  bool isActive,  DateTime createdAt,  DateTime updatedAt,  VehicleMake? makeRef,  VehicleModel? modelRef,  VehicleYear? yearRef,  VehicleTrim? trimRef,  VehicleEngine? engineRef)?  $default,) {final _that = this;
switch (_that) {
case _UserVehicle() when $default != null:
return $default(_that.id,_that.userId,_that.make,_that.model,_that.year,_that.trim,_that.engine,_that.color,_that.vin,_that.licensePlate,_that.mileage,_that.condition,_that.purchaseDate,_that.purchasePrice,_that.insuranceExpiry,_that.lastServiceDate,_that.nextServiceDue,_that.imageUrls,_that.isPrimary,_that.isActive,_that.createdAt,_that.updatedAt,_that.makeRef,_that.modelRef,_that.yearRef,_that.trimRef,_that.engineRef);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _UserVehicle implements UserVehicle {
  const _UserVehicle({required this.id, required this.userId, required this.make, required this.model, required this.year, this.trim, this.engine, this.color, this.vin, this.licensePlate, this.mileage = 0, this.condition = 'good', this.purchaseDate, this.purchasePrice, this.insuranceExpiry, this.lastServiceDate, this.nextServiceDue, final  List<String> imageUrls = const [], this.isPrimary = false, this.isActive = true, required this.createdAt, required this.updatedAt, this.makeRef, this.modelRef, this.yearRef, this.trimRef, this.engineRef}): _imageUrls = imageUrls;
  factory _UserVehicle.fromJson(Map<String, dynamic> json) => _$UserVehicleFromJson(json);

@override final  int id;
@override final  String userId;
@override final  String make;
@override final  String model;
@override final  int year;
@override final  String? trim;
@override final  String? engine;
@override final  String? color;
@override final  String? vin;
@override final  String? licensePlate;
@override@JsonKey() final  int mileage;
@override@JsonKey() final  String condition;
@override final  DateTime? purchaseDate;
@override final  double? purchasePrice;
@override final  DateTime? insuranceExpiry;
@override final  DateTime? lastServiceDate;
@override final  DateTime? nextServiceDue;
 final  List<String> _imageUrls;
@override@JsonKey() List<String> get imageUrls {
  if (_imageUrls is EqualUnmodifiableListView) return _imageUrls;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_imageUrls);
}

@override@JsonKey() final  bool isPrimary;
@override@JsonKey() final  bool isActive;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;
// Enhanced reference data
@override final  VehicleMake? makeRef;
@override final  VehicleModel? modelRef;
@override final  VehicleYear? yearRef;
@override final  VehicleTrim? trimRef;
@override final  VehicleEngine? engineRef;

/// Create a copy of UserVehicle
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserVehicleCopyWith<_UserVehicle> get copyWith => __$UserVehicleCopyWithImpl<_UserVehicle>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserVehicleToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserVehicle&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.make, make) || other.make == make)&&(identical(other.model, model) || other.model == model)&&(identical(other.year, year) || other.year == year)&&(identical(other.trim, trim) || other.trim == trim)&&(identical(other.engine, engine) || other.engine == engine)&&(identical(other.color, color) || other.color == color)&&(identical(other.vin, vin) || other.vin == vin)&&(identical(other.licensePlate, licensePlate) || other.licensePlate == licensePlate)&&(identical(other.mileage, mileage) || other.mileage == mileage)&&(identical(other.condition, condition) || other.condition == condition)&&(identical(other.purchaseDate, purchaseDate) || other.purchaseDate == purchaseDate)&&(identical(other.purchasePrice, purchasePrice) || other.purchasePrice == purchasePrice)&&(identical(other.insuranceExpiry, insuranceExpiry) || other.insuranceExpiry == insuranceExpiry)&&(identical(other.lastServiceDate, lastServiceDate) || other.lastServiceDate == lastServiceDate)&&(identical(other.nextServiceDue, nextServiceDue) || other.nextServiceDue == nextServiceDue)&&const DeepCollectionEquality().equals(other._imageUrls, _imageUrls)&&(identical(other.isPrimary, isPrimary) || other.isPrimary == isPrimary)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.makeRef, makeRef) || other.makeRef == makeRef)&&(identical(other.modelRef, modelRef) || other.modelRef == modelRef)&&(identical(other.yearRef, yearRef) || other.yearRef == yearRef)&&(identical(other.trimRef, trimRef) || other.trimRef == trimRef)&&(identical(other.engineRef, engineRef) || other.engineRef == engineRef));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,make,model,year,trim,engine,color,vin,licensePlate,mileage,condition,purchaseDate,purchasePrice,insuranceExpiry,lastServiceDate,nextServiceDue,const DeepCollectionEquality().hash(_imageUrls),isPrimary,isActive,createdAt,updatedAt,makeRef,modelRef,yearRef,trimRef,engineRef]);

@override
String toString() {
  return 'UserVehicle(id: $id, userId: $userId, make: $make, model: $model, year: $year, trim: $trim, engine: $engine, color: $color, vin: $vin, licensePlate: $licensePlate, mileage: $mileage, condition: $condition, purchaseDate: $purchaseDate, purchasePrice: $purchasePrice, insuranceExpiry: $insuranceExpiry, lastServiceDate: $lastServiceDate, nextServiceDue: $nextServiceDue, imageUrls: $imageUrls, isPrimary: $isPrimary, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, makeRef: $makeRef, modelRef: $modelRef, yearRef: $yearRef, trimRef: $trimRef, engineRef: $engineRef)';
}


}

/// @nodoc
abstract mixin class _$UserVehicleCopyWith<$Res> implements $UserVehicleCopyWith<$Res> {
  factory _$UserVehicleCopyWith(_UserVehicle value, $Res Function(_UserVehicle) _then) = __$UserVehicleCopyWithImpl;
@override @useResult
$Res call({
 int id, String userId, String make, String model, int year, String? trim, String? engine, String? color, String? vin, String? licensePlate, int mileage, String condition, DateTime? purchaseDate, double? purchasePrice, DateTime? insuranceExpiry, DateTime? lastServiceDate, DateTime? nextServiceDue, List<String> imageUrls, bool isPrimary, bool isActive, DateTime createdAt, DateTime updatedAt, VehicleMake? makeRef, VehicleModel? modelRef, VehicleYear? yearRef, VehicleTrim? trimRef, VehicleEngine? engineRef
});


@override $VehicleMakeCopyWith<$Res>? get makeRef;@override $VehicleModelCopyWith<$Res>? get modelRef;@override $VehicleYearCopyWith<$Res>? get yearRef;@override $VehicleTrimCopyWith<$Res>? get trimRef;@override $VehicleEngineCopyWith<$Res>? get engineRef;

}
/// @nodoc
class __$UserVehicleCopyWithImpl<$Res>
    implements _$UserVehicleCopyWith<$Res> {
  __$UserVehicleCopyWithImpl(this._self, this._then);

  final _UserVehicle _self;
  final $Res Function(_UserVehicle) _then;

/// Create a copy of UserVehicle
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? make = null,Object? model = null,Object? year = null,Object? trim = freezed,Object? engine = freezed,Object? color = freezed,Object? vin = freezed,Object? licensePlate = freezed,Object? mileage = null,Object? condition = null,Object? purchaseDate = freezed,Object? purchasePrice = freezed,Object? insuranceExpiry = freezed,Object? lastServiceDate = freezed,Object? nextServiceDue = freezed,Object? imageUrls = null,Object? isPrimary = null,Object? isActive = null,Object? createdAt = null,Object? updatedAt = null,Object? makeRef = freezed,Object? modelRef = freezed,Object? yearRef = freezed,Object? trimRef = freezed,Object? engineRef = freezed,}) {
  return _then(_UserVehicle(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,make: null == make ? _self.make : make // ignore: cast_nullable_to_non_nullable
as String,model: null == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,trim: freezed == trim ? _self.trim : trim // ignore: cast_nullable_to_non_nullable
as String?,engine: freezed == engine ? _self.engine : engine // ignore: cast_nullable_to_non_nullable
as String?,color: freezed == color ? _self.color : color // ignore: cast_nullable_to_non_nullable
as String?,vin: freezed == vin ? _self.vin : vin // ignore: cast_nullable_to_non_nullable
as String?,licensePlate: freezed == licensePlate ? _self.licensePlate : licensePlate // ignore: cast_nullable_to_non_nullable
as String?,mileage: null == mileage ? _self.mileage : mileage // ignore: cast_nullable_to_non_nullable
as int,condition: null == condition ? _self.condition : condition // ignore: cast_nullable_to_non_nullable
as String,purchaseDate: freezed == purchaseDate ? _self.purchaseDate : purchaseDate // ignore: cast_nullable_to_non_nullable
as DateTime?,purchasePrice: freezed == purchasePrice ? _self.purchasePrice : purchasePrice // ignore: cast_nullable_to_non_nullable
as double?,insuranceExpiry: freezed == insuranceExpiry ? _self.insuranceExpiry : insuranceExpiry // ignore: cast_nullable_to_non_nullable
as DateTime?,lastServiceDate: freezed == lastServiceDate ? _self.lastServiceDate : lastServiceDate // ignore: cast_nullable_to_non_nullable
as DateTime?,nextServiceDue: freezed == nextServiceDue ? _self.nextServiceDue : nextServiceDue // ignore: cast_nullable_to_non_nullable
as DateTime?,imageUrls: null == imageUrls ? _self._imageUrls : imageUrls // ignore: cast_nullable_to_non_nullable
as List<String>,isPrimary: null == isPrimary ? _self.isPrimary : isPrimary // ignore: cast_nullable_to_non_nullable
as bool,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,makeRef: freezed == makeRef ? _self.makeRef : makeRef // ignore: cast_nullable_to_non_nullable
as VehicleMake?,modelRef: freezed == modelRef ? _self.modelRef : modelRef // ignore: cast_nullable_to_non_nullable
as VehicleModel?,yearRef: freezed == yearRef ? _self.yearRef : yearRef // ignore: cast_nullable_to_non_nullable
as VehicleYear?,trimRef: freezed == trimRef ? _self.trimRef : trimRef // ignore: cast_nullable_to_non_nullable
as VehicleTrim?,engineRef: freezed == engineRef ? _self.engineRef : engineRef // ignore: cast_nullable_to_non_nullable
as VehicleEngine?,
  ));
}

/// Create a copy of UserVehicle
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VehicleMakeCopyWith<$Res>? get makeRef {
    if (_self.makeRef == null) {
    return null;
  }

  return $VehicleMakeCopyWith<$Res>(_self.makeRef!, (value) {
    return _then(_self.copyWith(makeRef: value));
  });
}/// Create a copy of UserVehicle
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VehicleModelCopyWith<$Res>? get modelRef {
    if (_self.modelRef == null) {
    return null;
  }

  return $VehicleModelCopyWith<$Res>(_self.modelRef!, (value) {
    return _then(_self.copyWith(modelRef: value));
  });
}/// Create a copy of UserVehicle
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VehicleYearCopyWith<$Res>? get yearRef {
    if (_self.yearRef == null) {
    return null;
  }

  return $VehicleYearCopyWith<$Res>(_self.yearRef!, (value) {
    return _then(_self.copyWith(yearRef: value));
  });
}/// Create a copy of UserVehicle
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VehicleTrimCopyWith<$Res>? get trimRef {
    if (_self.trimRef == null) {
    return null;
  }

  return $VehicleTrimCopyWith<$Res>(_self.trimRef!, (value) {
    return _then(_self.copyWith(trimRef: value));
  });
}/// Create a copy of UserVehicle
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VehicleEngineCopyWith<$Res>? get engineRef {
    if (_self.engineRef == null) {
    return null;
  }

  return $VehicleEngineCopyWith<$Res>(_self.engineRef!, (value) {
    return _then(_self.copyWith(engineRef: value));
  });
}
}


/// @nodoc
mixin _$UserVehicleListResponse {

 List<UserVehicle> get vehicles; int get total; bool get hasMore;
/// Create a copy of UserVehicleListResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserVehicleListResponseCopyWith<UserVehicleListResponse> get copyWith => _$UserVehicleListResponseCopyWithImpl<UserVehicleListResponse>(this as UserVehicleListResponse, _$identity);

  /// Serializes this UserVehicleListResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserVehicleListResponse&&const DeepCollectionEquality().equals(other.vehicles, vehicles)&&(identical(other.total, total) || other.total == total)&&(identical(other.hasMore, hasMore) || other.hasMore == hasMore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(vehicles),total,hasMore);

@override
String toString() {
  return 'UserVehicleListResponse(vehicles: $vehicles, total: $total, hasMore: $hasMore)';
}


}

/// @nodoc
abstract mixin class $UserVehicleListResponseCopyWith<$Res>  {
  factory $UserVehicleListResponseCopyWith(UserVehicleListResponse value, $Res Function(UserVehicleListResponse) _then) = _$UserVehicleListResponseCopyWithImpl;
@useResult
$Res call({
 List<UserVehicle> vehicles, int total, bool hasMore
});




}
/// @nodoc
class _$UserVehicleListResponseCopyWithImpl<$Res>
    implements $UserVehicleListResponseCopyWith<$Res> {
  _$UserVehicleListResponseCopyWithImpl(this._self, this._then);

  final UserVehicleListResponse _self;
  final $Res Function(UserVehicleListResponse) _then;

/// Create a copy of UserVehicleListResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? vehicles = null,Object? total = null,Object? hasMore = null,}) {
  return _then(_self.copyWith(
vehicles: null == vehicles ? _self.vehicles : vehicles // ignore: cast_nullable_to_non_nullable
as List<UserVehicle>,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int,hasMore: null == hasMore ? _self.hasMore : hasMore // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [UserVehicleListResponse].
extension UserVehicleListResponsePatterns on UserVehicleListResponse {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UserVehicleListResponse value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UserVehicleListResponse() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UserVehicleListResponse value)  $default,){
final _that = this;
switch (_that) {
case _UserVehicleListResponse():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UserVehicleListResponse value)?  $default,){
final _that = this;
switch (_that) {
case _UserVehicleListResponse() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<UserVehicle> vehicles,  int total,  bool hasMore)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UserVehicleListResponse() when $default != null:
return $default(_that.vehicles,_that.total,_that.hasMore);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<UserVehicle> vehicles,  int total,  bool hasMore)  $default,) {final _that = this;
switch (_that) {
case _UserVehicleListResponse():
return $default(_that.vehicles,_that.total,_that.hasMore);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<UserVehicle> vehicles,  int total,  bool hasMore)?  $default,) {final _that = this;
switch (_that) {
case _UserVehicleListResponse() when $default != null:
return $default(_that.vehicles,_that.total,_that.hasMore);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _UserVehicleListResponse implements UserVehicleListResponse {
  const _UserVehicleListResponse({required final  List<UserVehicle> vehicles, required this.total, this.hasMore = false}): _vehicles = vehicles;
  factory _UserVehicleListResponse.fromJson(Map<String, dynamic> json) => _$UserVehicleListResponseFromJson(json);

 final  List<UserVehicle> _vehicles;
@override List<UserVehicle> get vehicles {
  if (_vehicles is EqualUnmodifiableListView) return _vehicles;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_vehicles);
}

@override final  int total;
@override@JsonKey() final  bool hasMore;

/// Create a copy of UserVehicleListResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserVehicleListResponseCopyWith<_UserVehicleListResponse> get copyWith => __$UserVehicleListResponseCopyWithImpl<_UserVehicleListResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserVehicleListResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserVehicleListResponse&&const DeepCollectionEquality().equals(other._vehicles, _vehicles)&&(identical(other.total, total) || other.total == total)&&(identical(other.hasMore, hasMore) || other.hasMore == hasMore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_vehicles),total,hasMore);

@override
String toString() {
  return 'UserVehicleListResponse(vehicles: $vehicles, total: $total, hasMore: $hasMore)';
}


}

/// @nodoc
abstract mixin class _$UserVehicleListResponseCopyWith<$Res> implements $UserVehicleListResponseCopyWith<$Res> {
  factory _$UserVehicleListResponseCopyWith(_UserVehicleListResponse value, $Res Function(_UserVehicleListResponse) _then) = __$UserVehicleListResponseCopyWithImpl;
@override @useResult
$Res call({
 List<UserVehicle> vehicles, int total, bool hasMore
});




}
/// @nodoc
class __$UserVehicleListResponseCopyWithImpl<$Res>
    implements _$UserVehicleListResponseCopyWith<$Res> {
  __$UserVehicleListResponseCopyWithImpl(this._self, this._then);

  final _UserVehicleListResponse _self;
  final $Res Function(_UserVehicleListResponse) _then;

/// Create a copy of UserVehicleListResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? vehicles = null,Object? total = null,Object? hasMore = null,}) {
  return _then(_UserVehicleListResponse(
vehicles: null == vehicles ? _self._vehicles : vehicles // ignore: cast_nullable_to_non_nullable
as List<UserVehicle>,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int,hasMore: null == hasMore ? _self.hasMore : hasMore // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$CreateUserVehicleRequest {

 int get makeRefId; int get modelRefId; int? get yearRefId; int? get trimRefId; int? get engineRefId; String? get color; String? get vin; String? get licensePlate; int get mileage; String? get notes; DateTime? get purchaseDate; double? get purchasePrice; DateTime? get insuranceExpiry; DateTime? get lastServiceDate; DateTime? get nextServiceDue; String get condition; List<String> get imageUrls; bool get isPrimary;
/// Create a copy of CreateUserVehicleRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CreateUserVehicleRequestCopyWith<CreateUserVehicleRequest> get copyWith => _$CreateUserVehicleRequestCopyWithImpl<CreateUserVehicleRequest>(this as CreateUserVehicleRequest, _$identity);

  /// Serializes this CreateUserVehicleRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreateUserVehicleRequest&&(identical(other.makeRefId, makeRefId) || other.makeRefId == makeRefId)&&(identical(other.modelRefId, modelRefId) || other.modelRefId == modelRefId)&&(identical(other.yearRefId, yearRefId) || other.yearRefId == yearRefId)&&(identical(other.trimRefId, trimRefId) || other.trimRefId == trimRefId)&&(identical(other.engineRefId, engineRefId) || other.engineRefId == engineRefId)&&(identical(other.color, color) || other.color == color)&&(identical(other.vin, vin) || other.vin == vin)&&(identical(other.licensePlate, licensePlate) || other.licensePlate == licensePlate)&&(identical(other.mileage, mileage) || other.mileage == mileage)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.purchaseDate, purchaseDate) || other.purchaseDate == purchaseDate)&&(identical(other.purchasePrice, purchasePrice) || other.purchasePrice == purchasePrice)&&(identical(other.insuranceExpiry, insuranceExpiry) || other.insuranceExpiry == insuranceExpiry)&&(identical(other.lastServiceDate, lastServiceDate) || other.lastServiceDate == lastServiceDate)&&(identical(other.nextServiceDue, nextServiceDue) || other.nextServiceDue == nextServiceDue)&&(identical(other.condition, condition) || other.condition == condition)&&const DeepCollectionEquality().equals(other.imageUrls, imageUrls)&&(identical(other.isPrimary, isPrimary) || other.isPrimary == isPrimary));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,makeRefId,modelRefId,yearRefId,trimRefId,engineRefId,color,vin,licensePlate,mileage,notes,purchaseDate,purchasePrice,insuranceExpiry,lastServiceDate,nextServiceDue,condition,const DeepCollectionEquality().hash(imageUrls),isPrimary);

@override
String toString() {
  return 'CreateUserVehicleRequest(makeRefId: $makeRefId, modelRefId: $modelRefId, yearRefId: $yearRefId, trimRefId: $trimRefId, engineRefId: $engineRefId, color: $color, vin: $vin, licensePlate: $licensePlate, mileage: $mileage, notes: $notes, purchaseDate: $purchaseDate, purchasePrice: $purchasePrice, insuranceExpiry: $insuranceExpiry, lastServiceDate: $lastServiceDate, nextServiceDue: $nextServiceDue, condition: $condition, imageUrls: $imageUrls, isPrimary: $isPrimary)';
}


}

/// @nodoc
abstract mixin class $CreateUserVehicleRequestCopyWith<$Res>  {
  factory $CreateUserVehicleRequestCopyWith(CreateUserVehicleRequest value, $Res Function(CreateUserVehicleRequest) _then) = _$CreateUserVehicleRequestCopyWithImpl;
@useResult
$Res call({
 int makeRefId, int modelRefId, int? yearRefId, int? trimRefId, int? engineRefId, String? color, String? vin, String? licensePlate, int mileage, String? notes, DateTime? purchaseDate, double? purchasePrice, DateTime? insuranceExpiry, DateTime? lastServiceDate, DateTime? nextServiceDue, String condition, List<String> imageUrls, bool isPrimary
});




}
/// @nodoc
class _$CreateUserVehicleRequestCopyWithImpl<$Res>
    implements $CreateUserVehicleRequestCopyWith<$Res> {
  _$CreateUserVehicleRequestCopyWithImpl(this._self, this._then);

  final CreateUserVehicleRequest _self;
  final $Res Function(CreateUserVehicleRequest) _then;

/// Create a copy of CreateUserVehicleRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? makeRefId = null,Object? modelRefId = null,Object? yearRefId = freezed,Object? trimRefId = freezed,Object? engineRefId = freezed,Object? color = freezed,Object? vin = freezed,Object? licensePlate = freezed,Object? mileage = null,Object? notes = freezed,Object? purchaseDate = freezed,Object? purchasePrice = freezed,Object? insuranceExpiry = freezed,Object? lastServiceDate = freezed,Object? nextServiceDue = freezed,Object? condition = null,Object? imageUrls = null,Object? isPrimary = null,}) {
  return _then(_self.copyWith(
makeRefId: null == makeRefId ? _self.makeRefId : makeRefId // ignore: cast_nullable_to_non_nullable
as int,modelRefId: null == modelRefId ? _self.modelRefId : modelRefId // ignore: cast_nullable_to_non_nullable
as int,yearRefId: freezed == yearRefId ? _self.yearRefId : yearRefId // ignore: cast_nullable_to_non_nullable
as int?,trimRefId: freezed == trimRefId ? _self.trimRefId : trimRefId // ignore: cast_nullable_to_non_nullable
as int?,engineRefId: freezed == engineRefId ? _self.engineRefId : engineRefId // ignore: cast_nullable_to_non_nullable
as int?,color: freezed == color ? _self.color : color // ignore: cast_nullable_to_non_nullable
as String?,vin: freezed == vin ? _self.vin : vin // ignore: cast_nullable_to_non_nullable
as String?,licensePlate: freezed == licensePlate ? _self.licensePlate : licensePlate // ignore: cast_nullable_to_non_nullable
as String?,mileage: null == mileage ? _self.mileage : mileage // ignore: cast_nullable_to_non_nullable
as int,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,purchaseDate: freezed == purchaseDate ? _self.purchaseDate : purchaseDate // ignore: cast_nullable_to_non_nullable
as DateTime?,purchasePrice: freezed == purchasePrice ? _self.purchasePrice : purchasePrice // ignore: cast_nullable_to_non_nullable
as double?,insuranceExpiry: freezed == insuranceExpiry ? _self.insuranceExpiry : insuranceExpiry // ignore: cast_nullable_to_non_nullable
as DateTime?,lastServiceDate: freezed == lastServiceDate ? _self.lastServiceDate : lastServiceDate // ignore: cast_nullable_to_non_nullable
as DateTime?,nextServiceDue: freezed == nextServiceDue ? _self.nextServiceDue : nextServiceDue // ignore: cast_nullable_to_non_nullable
as DateTime?,condition: null == condition ? _self.condition : condition // ignore: cast_nullable_to_non_nullable
as String,imageUrls: null == imageUrls ? _self.imageUrls : imageUrls // ignore: cast_nullable_to_non_nullable
as List<String>,isPrimary: null == isPrimary ? _self.isPrimary : isPrimary // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [CreateUserVehicleRequest].
extension CreateUserVehicleRequestPatterns on CreateUserVehicleRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CreateUserVehicleRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CreateUserVehicleRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CreateUserVehicleRequest value)  $default,){
final _that = this;
switch (_that) {
case _CreateUserVehicleRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CreateUserVehicleRequest value)?  $default,){
final _that = this;
switch (_that) {
case _CreateUserVehicleRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int makeRefId,  int modelRefId,  int? yearRefId,  int? trimRefId,  int? engineRefId,  String? color,  String? vin,  String? licensePlate,  int mileage,  String? notes,  DateTime? purchaseDate,  double? purchasePrice,  DateTime? insuranceExpiry,  DateTime? lastServiceDate,  DateTime? nextServiceDue,  String condition,  List<String> imageUrls,  bool isPrimary)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CreateUserVehicleRequest() when $default != null:
return $default(_that.makeRefId,_that.modelRefId,_that.yearRefId,_that.trimRefId,_that.engineRefId,_that.color,_that.vin,_that.licensePlate,_that.mileage,_that.notes,_that.purchaseDate,_that.purchasePrice,_that.insuranceExpiry,_that.lastServiceDate,_that.nextServiceDue,_that.condition,_that.imageUrls,_that.isPrimary);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int makeRefId,  int modelRefId,  int? yearRefId,  int? trimRefId,  int? engineRefId,  String? color,  String? vin,  String? licensePlate,  int mileage,  String? notes,  DateTime? purchaseDate,  double? purchasePrice,  DateTime? insuranceExpiry,  DateTime? lastServiceDate,  DateTime? nextServiceDue,  String condition,  List<String> imageUrls,  bool isPrimary)  $default,) {final _that = this;
switch (_that) {
case _CreateUserVehicleRequest():
return $default(_that.makeRefId,_that.modelRefId,_that.yearRefId,_that.trimRefId,_that.engineRefId,_that.color,_that.vin,_that.licensePlate,_that.mileage,_that.notes,_that.purchaseDate,_that.purchasePrice,_that.insuranceExpiry,_that.lastServiceDate,_that.nextServiceDue,_that.condition,_that.imageUrls,_that.isPrimary);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int makeRefId,  int modelRefId,  int? yearRefId,  int? trimRefId,  int? engineRefId,  String? color,  String? vin,  String? licensePlate,  int mileage,  String? notes,  DateTime? purchaseDate,  double? purchasePrice,  DateTime? insuranceExpiry,  DateTime? lastServiceDate,  DateTime? nextServiceDue,  String condition,  List<String> imageUrls,  bool isPrimary)?  $default,) {final _that = this;
switch (_that) {
case _CreateUserVehicleRequest() when $default != null:
return $default(_that.makeRefId,_that.modelRefId,_that.yearRefId,_that.trimRefId,_that.engineRefId,_that.color,_that.vin,_that.licensePlate,_that.mileage,_that.notes,_that.purchaseDate,_that.purchasePrice,_that.insuranceExpiry,_that.lastServiceDate,_that.nextServiceDue,_that.condition,_that.imageUrls,_that.isPrimary);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CreateUserVehicleRequest implements CreateUserVehicleRequest {
  const _CreateUserVehicleRequest({required this.makeRefId, required this.modelRefId, this.yearRefId, this.trimRefId, this.engineRefId, this.color, this.vin, this.licensePlate, this.mileage = 0, this.notes, this.purchaseDate, this.purchasePrice, this.insuranceExpiry, this.lastServiceDate, this.nextServiceDue, this.condition = 'good', final  List<String> imageUrls = const [], this.isPrimary = false}): _imageUrls = imageUrls;
  factory _CreateUserVehicleRequest.fromJson(Map<String, dynamic> json) => _$CreateUserVehicleRequestFromJson(json);

@override final  int makeRefId;
@override final  int modelRefId;
@override final  int? yearRefId;
@override final  int? trimRefId;
@override final  int? engineRefId;
@override final  String? color;
@override final  String? vin;
@override final  String? licensePlate;
@override@JsonKey() final  int mileage;
@override final  String? notes;
@override final  DateTime? purchaseDate;
@override final  double? purchasePrice;
@override final  DateTime? insuranceExpiry;
@override final  DateTime? lastServiceDate;
@override final  DateTime? nextServiceDue;
@override@JsonKey() final  String condition;
 final  List<String> _imageUrls;
@override@JsonKey() List<String> get imageUrls {
  if (_imageUrls is EqualUnmodifiableListView) return _imageUrls;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_imageUrls);
}

@override@JsonKey() final  bool isPrimary;

/// Create a copy of CreateUserVehicleRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CreateUserVehicleRequestCopyWith<_CreateUserVehicleRequest> get copyWith => __$CreateUserVehicleRequestCopyWithImpl<_CreateUserVehicleRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CreateUserVehicleRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CreateUserVehicleRequest&&(identical(other.makeRefId, makeRefId) || other.makeRefId == makeRefId)&&(identical(other.modelRefId, modelRefId) || other.modelRefId == modelRefId)&&(identical(other.yearRefId, yearRefId) || other.yearRefId == yearRefId)&&(identical(other.trimRefId, trimRefId) || other.trimRefId == trimRefId)&&(identical(other.engineRefId, engineRefId) || other.engineRefId == engineRefId)&&(identical(other.color, color) || other.color == color)&&(identical(other.vin, vin) || other.vin == vin)&&(identical(other.licensePlate, licensePlate) || other.licensePlate == licensePlate)&&(identical(other.mileage, mileage) || other.mileage == mileage)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.purchaseDate, purchaseDate) || other.purchaseDate == purchaseDate)&&(identical(other.purchasePrice, purchasePrice) || other.purchasePrice == purchasePrice)&&(identical(other.insuranceExpiry, insuranceExpiry) || other.insuranceExpiry == insuranceExpiry)&&(identical(other.lastServiceDate, lastServiceDate) || other.lastServiceDate == lastServiceDate)&&(identical(other.nextServiceDue, nextServiceDue) || other.nextServiceDue == nextServiceDue)&&(identical(other.condition, condition) || other.condition == condition)&&const DeepCollectionEquality().equals(other._imageUrls, _imageUrls)&&(identical(other.isPrimary, isPrimary) || other.isPrimary == isPrimary));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,makeRefId,modelRefId,yearRefId,trimRefId,engineRefId,color,vin,licensePlate,mileage,notes,purchaseDate,purchasePrice,insuranceExpiry,lastServiceDate,nextServiceDue,condition,const DeepCollectionEquality().hash(_imageUrls),isPrimary);

@override
String toString() {
  return 'CreateUserVehicleRequest(makeRefId: $makeRefId, modelRefId: $modelRefId, yearRefId: $yearRefId, trimRefId: $trimRefId, engineRefId: $engineRefId, color: $color, vin: $vin, licensePlate: $licensePlate, mileage: $mileage, notes: $notes, purchaseDate: $purchaseDate, purchasePrice: $purchasePrice, insuranceExpiry: $insuranceExpiry, lastServiceDate: $lastServiceDate, nextServiceDue: $nextServiceDue, condition: $condition, imageUrls: $imageUrls, isPrimary: $isPrimary)';
}


}

/// @nodoc
abstract mixin class _$CreateUserVehicleRequestCopyWith<$Res> implements $CreateUserVehicleRequestCopyWith<$Res> {
  factory _$CreateUserVehicleRequestCopyWith(_CreateUserVehicleRequest value, $Res Function(_CreateUserVehicleRequest) _then) = __$CreateUserVehicleRequestCopyWithImpl;
@override @useResult
$Res call({
 int makeRefId, int modelRefId, int? yearRefId, int? trimRefId, int? engineRefId, String? color, String? vin, String? licensePlate, int mileage, String? notes, DateTime? purchaseDate, double? purchasePrice, DateTime? insuranceExpiry, DateTime? lastServiceDate, DateTime? nextServiceDue, String condition, List<String> imageUrls, bool isPrimary
});




}
/// @nodoc
class __$CreateUserVehicleRequestCopyWithImpl<$Res>
    implements _$CreateUserVehicleRequestCopyWith<$Res> {
  __$CreateUserVehicleRequestCopyWithImpl(this._self, this._then);

  final _CreateUserVehicleRequest _self;
  final $Res Function(_CreateUserVehicleRequest) _then;

/// Create a copy of CreateUserVehicleRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? makeRefId = null,Object? modelRefId = null,Object? yearRefId = freezed,Object? trimRefId = freezed,Object? engineRefId = freezed,Object? color = freezed,Object? vin = freezed,Object? licensePlate = freezed,Object? mileage = null,Object? notes = freezed,Object? purchaseDate = freezed,Object? purchasePrice = freezed,Object? insuranceExpiry = freezed,Object? lastServiceDate = freezed,Object? nextServiceDue = freezed,Object? condition = null,Object? imageUrls = null,Object? isPrimary = null,}) {
  return _then(_CreateUserVehicleRequest(
makeRefId: null == makeRefId ? _self.makeRefId : makeRefId // ignore: cast_nullable_to_non_nullable
as int,modelRefId: null == modelRefId ? _self.modelRefId : modelRefId // ignore: cast_nullable_to_non_nullable
as int,yearRefId: freezed == yearRefId ? _self.yearRefId : yearRefId // ignore: cast_nullable_to_non_nullable
as int?,trimRefId: freezed == trimRefId ? _self.trimRefId : trimRefId // ignore: cast_nullable_to_non_nullable
as int?,engineRefId: freezed == engineRefId ? _self.engineRefId : engineRefId // ignore: cast_nullable_to_non_nullable
as int?,color: freezed == color ? _self.color : color // ignore: cast_nullable_to_non_nullable
as String?,vin: freezed == vin ? _self.vin : vin // ignore: cast_nullable_to_non_nullable
as String?,licensePlate: freezed == licensePlate ? _self.licensePlate : licensePlate // ignore: cast_nullable_to_non_nullable
as String?,mileage: null == mileage ? _self.mileage : mileage // ignore: cast_nullable_to_non_nullable
as int,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,purchaseDate: freezed == purchaseDate ? _self.purchaseDate : purchaseDate // ignore: cast_nullable_to_non_nullable
as DateTime?,purchasePrice: freezed == purchasePrice ? _self.purchasePrice : purchasePrice // ignore: cast_nullable_to_non_nullable
as double?,insuranceExpiry: freezed == insuranceExpiry ? _self.insuranceExpiry : insuranceExpiry // ignore: cast_nullable_to_non_nullable
as DateTime?,lastServiceDate: freezed == lastServiceDate ? _self.lastServiceDate : lastServiceDate // ignore: cast_nullable_to_non_nullable
as DateTime?,nextServiceDue: freezed == nextServiceDue ? _self.nextServiceDue : nextServiceDue // ignore: cast_nullable_to_non_nullable
as DateTime?,condition: null == condition ? _self.condition : condition // ignore: cast_nullable_to_non_nullable
as String,imageUrls: null == imageUrls ? _self._imageUrls : imageUrls // ignore: cast_nullable_to_non_nullable
as List<String>,isPrimary: null == isPrimary ? _self.isPrimary : isPrimary // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$UpdateUserVehicleRequest {

 String? get color; String? get vin; String? get licensePlate; int? get mileage; String? get notes; DateTime? get purchaseDate; double? get purchasePrice; DateTime? get insuranceExpiry; DateTime? get lastServiceDate; DateTime? get nextServiceDue; String? get condition; List<String>? get imageUrls; bool? get isPrimary; bool? get isActive;
/// Create a copy of UpdateUserVehicleRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateUserVehicleRequestCopyWith<UpdateUserVehicleRequest> get copyWith => _$UpdateUserVehicleRequestCopyWithImpl<UpdateUserVehicleRequest>(this as UpdateUserVehicleRequest, _$identity);

  /// Serializes this UpdateUserVehicleRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateUserVehicleRequest&&(identical(other.color, color) || other.color == color)&&(identical(other.vin, vin) || other.vin == vin)&&(identical(other.licensePlate, licensePlate) || other.licensePlate == licensePlate)&&(identical(other.mileage, mileage) || other.mileage == mileage)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.purchaseDate, purchaseDate) || other.purchaseDate == purchaseDate)&&(identical(other.purchasePrice, purchasePrice) || other.purchasePrice == purchasePrice)&&(identical(other.insuranceExpiry, insuranceExpiry) || other.insuranceExpiry == insuranceExpiry)&&(identical(other.lastServiceDate, lastServiceDate) || other.lastServiceDate == lastServiceDate)&&(identical(other.nextServiceDue, nextServiceDue) || other.nextServiceDue == nextServiceDue)&&(identical(other.condition, condition) || other.condition == condition)&&const DeepCollectionEquality().equals(other.imageUrls, imageUrls)&&(identical(other.isPrimary, isPrimary) || other.isPrimary == isPrimary)&&(identical(other.isActive, isActive) || other.isActive == isActive));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,color,vin,licensePlate,mileage,notes,purchaseDate,purchasePrice,insuranceExpiry,lastServiceDate,nextServiceDue,condition,const DeepCollectionEquality().hash(imageUrls),isPrimary,isActive);

@override
String toString() {
  return 'UpdateUserVehicleRequest(color: $color, vin: $vin, licensePlate: $licensePlate, mileage: $mileage, notes: $notes, purchaseDate: $purchaseDate, purchasePrice: $purchasePrice, insuranceExpiry: $insuranceExpiry, lastServiceDate: $lastServiceDate, nextServiceDue: $nextServiceDue, condition: $condition, imageUrls: $imageUrls, isPrimary: $isPrimary, isActive: $isActive)';
}


}

/// @nodoc
abstract mixin class $UpdateUserVehicleRequestCopyWith<$Res>  {
  factory $UpdateUserVehicleRequestCopyWith(UpdateUserVehicleRequest value, $Res Function(UpdateUserVehicleRequest) _then) = _$UpdateUserVehicleRequestCopyWithImpl;
@useResult
$Res call({
 String? color, String? vin, String? licensePlate, int? mileage, String? notes, DateTime? purchaseDate, double? purchasePrice, DateTime? insuranceExpiry, DateTime? lastServiceDate, DateTime? nextServiceDue, String? condition, List<String>? imageUrls, bool? isPrimary, bool? isActive
});




}
/// @nodoc
class _$UpdateUserVehicleRequestCopyWithImpl<$Res>
    implements $UpdateUserVehicleRequestCopyWith<$Res> {
  _$UpdateUserVehicleRequestCopyWithImpl(this._self, this._then);

  final UpdateUserVehicleRequest _self;
  final $Res Function(UpdateUserVehicleRequest) _then;

/// Create a copy of UpdateUserVehicleRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? color = freezed,Object? vin = freezed,Object? licensePlate = freezed,Object? mileage = freezed,Object? notes = freezed,Object? purchaseDate = freezed,Object? purchasePrice = freezed,Object? insuranceExpiry = freezed,Object? lastServiceDate = freezed,Object? nextServiceDue = freezed,Object? condition = freezed,Object? imageUrls = freezed,Object? isPrimary = freezed,Object? isActive = freezed,}) {
  return _then(_self.copyWith(
color: freezed == color ? _self.color : color // ignore: cast_nullable_to_non_nullable
as String?,vin: freezed == vin ? _self.vin : vin // ignore: cast_nullable_to_non_nullable
as String?,licensePlate: freezed == licensePlate ? _self.licensePlate : licensePlate // ignore: cast_nullable_to_non_nullable
as String?,mileage: freezed == mileage ? _self.mileage : mileage // ignore: cast_nullable_to_non_nullable
as int?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,purchaseDate: freezed == purchaseDate ? _self.purchaseDate : purchaseDate // ignore: cast_nullable_to_non_nullable
as DateTime?,purchasePrice: freezed == purchasePrice ? _self.purchasePrice : purchasePrice // ignore: cast_nullable_to_non_nullable
as double?,insuranceExpiry: freezed == insuranceExpiry ? _self.insuranceExpiry : insuranceExpiry // ignore: cast_nullable_to_non_nullable
as DateTime?,lastServiceDate: freezed == lastServiceDate ? _self.lastServiceDate : lastServiceDate // ignore: cast_nullable_to_non_nullable
as DateTime?,nextServiceDue: freezed == nextServiceDue ? _self.nextServiceDue : nextServiceDue // ignore: cast_nullable_to_non_nullable
as DateTime?,condition: freezed == condition ? _self.condition : condition // ignore: cast_nullable_to_non_nullable
as String?,imageUrls: freezed == imageUrls ? _self.imageUrls : imageUrls // ignore: cast_nullable_to_non_nullable
as List<String>?,isPrimary: freezed == isPrimary ? _self.isPrimary : isPrimary // ignore: cast_nullable_to_non_nullable
as bool?,isActive: freezed == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}

}


/// Adds pattern-matching-related methods to [UpdateUserVehicleRequest].
extension UpdateUserVehicleRequestPatterns on UpdateUserVehicleRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UpdateUserVehicleRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UpdateUserVehicleRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UpdateUserVehicleRequest value)  $default,){
final _that = this;
switch (_that) {
case _UpdateUserVehicleRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UpdateUserVehicleRequest value)?  $default,){
final _that = this;
switch (_that) {
case _UpdateUserVehicleRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String? color,  String? vin,  String? licensePlate,  int? mileage,  String? notes,  DateTime? purchaseDate,  double? purchasePrice,  DateTime? insuranceExpiry,  DateTime? lastServiceDate,  DateTime? nextServiceDue,  String? condition,  List<String>? imageUrls,  bool? isPrimary,  bool? isActive)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UpdateUserVehicleRequest() when $default != null:
return $default(_that.color,_that.vin,_that.licensePlate,_that.mileage,_that.notes,_that.purchaseDate,_that.purchasePrice,_that.insuranceExpiry,_that.lastServiceDate,_that.nextServiceDue,_that.condition,_that.imageUrls,_that.isPrimary,_that.isActive);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String? color,  String? vin,  String? licensePlate,  int? mileage,  String? notes,  DateTime? purchaseDate,  double? purchasePrice,  DateTime? insuranceExpiry,  DateTime? lastServiceDate,  DateTime? nextServiceDue,  String? condition,  List<String>? imageUrls,  bool? isPrimary,  bool? isActive)  $default,) {final _that = this;
switch (_that) {
case _UpdateUserVehicleRequest():
return $default(_that.color,_that.vin,_that.licensePlate,_that.mileage,_that.notes,_that.purchaseDate,_that.purchasePrice,_that.insuranceExpiry,_that.lastServiceDate,_that.nextServiceDue,_that.condition,_that.imageUrls,_that.isPrimary,_that.isActive);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String? color,  String? vin,  String? licensePlate,  int? mileage,  String? notes,  DateTime? purchaseDate,  double? purchasePrice,  DateTime? insuranceExpiry,  DateTime? lastServiceDate,  DateTime? nextServiceDue,  String? condition,  List<String>? imageUrls,  bool? isPrimary,  bool? isActive)?  $default,) {final _that = this;
switch (_that) {
case _UpdateUserVehicleRequest() when $default != null:
return $default(_that.color,_that.vin,_that.licensePlate,_that.mileage,_that.notes,_that.purchaseDate,_that.purchasePrice,_that.insuranceExpiry,_that.lastServiceDate,_that.nextServiceDue,_that.condition,_that.imageUrls,_that.isPrimary,_that.isActive);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _UpdateUserVehicleRequest implements UpdateUserVehicleRequest {
  const _UpdateUserVehicleRequest({this.color, this.vin, this.licensePlate, this.mileage, this.notes, this.purchaseDate, this.purchasePrice, this.insuranceExpiry, this.lastServiceDate, this.nextServiceDue, this.condition, final  List<String>? imageUrls, this.isPrimary, this.isActive}): _imageUrls = imageUrls;
  factory _UpdateUserVehicleRequest.fromJson(Map<String, dynamic> json) => _$UpdateUserVehicleRequestFromJson(json);

@override final  String? color;
@override final  String? vin;
@override final  String? licensePlate;
@override final  int? mileage;
@override final  String? notes;
@override final  DateTime? purchaseDate;
@override final  double? purchasePrice;
@override final  DateTime? insuranceExpiry;
@override final  DateTime? lastServiceDate;
@override final  DateTime? nextServiceDue;
@override final  String? condition;
 final  List<String>? _imageUrls;
@override List<String>? get imageUrls {
  final value = _imageUrls;
  if (value == null) return null;
  if (_imageUrls is EqualUnmodifiableListView) return _imageUrls;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  bool? isPrimary;
@override final  bool? isActive;

/// Create a copy of UpdateUserVehicleRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdateUserVehicleRequestCopyWith<_UpdateUserVehicleRequest> get copyWith => __$UpdateUserVehicleRequestCopyWithImpl<_UpdateUserVehicleRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UpdateUserVehicleRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdateUserVehicleRequest&&(identical(other.color, color) || other.color == color)&&(identical(other.vin, vin) || other.vin == vin)&&(identical(other.licensePlate, licensePlate) || other.licensePlate == licensePlate)&&(identical(other.mileage, mileage) || other.mileage == mileage)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.purchaseDate, purchaseDate) || other.purchaseDate == purchaseDate)&&(identical(other.purchasePrice, purchasePrice) || other.purchasePrice == purchasePrice)&&(identical(other.insuranceExpiry, insuranceExpiry) || other.insuranceExpiry == insuranceExpiry)&&(identical(other.lastServiceDate, lastServiceDate) || other.lastServiceDate == lastServiceDate)&&(identical(other.nextServiceDue, nextServiceDue) || other.nextServiceDue == nextServiceDue)&&(identical(other.condition, condition) || other.condition == condition)&&const DeepCollectionEquality().equals(other._imageUrls, _imageUrls)&&(identical(other.isPrimary, isPrimary) || other.isPrimary == isPrimary)&&(identical(other.isActive, isActive) || other.isActive == isActive));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,color,vin,licensePlate,mileage,notes,purchaseDate,purchasePrice,insuranceExpiry,lastServiceDate,nextServiceDue,condition,const DeepCollectionEquality().hash(_imageUrls),isPrimary,isActive);

@override
String toString() {
  return 'UpdateUserVehicleRequest(color: $color, vin: $vin, licensePlate: $licensePlate, mileage: $mileage, notes: $notes, purchaseDate: $purchaseDate, purchasePrice: $purchasePrice, insuranceExpiry: $insuranceExpiry, lastServiceDate: $lastServiceDate, nextServiceDue: $nextServiceDue, condition: $condition, imageUrls: $imageUrls, isPrimary: $isPrimary, isActive: $isActive)';
}


}

/// @nodoc
abstract mixin class _$UpdateUserVehicleRequestCopyWith<$Res> implements $UpdateUserVehicleRequestCopyWith<$Res> {
  factory _$UpdateUserVehicleRequestCopyWith(_UpdateUserVehicleRequest value, $Res Function(_UpdateUserVehicleRequest) _then) = __$UpdateUserVehicleRequestCopyWithImpl;
@override @useResult
$Res call({
 String? color, String? vin, String? licensePlate, int? mileage, String? notes, DateTime? purchaseDate, double? purchasePrice, DateTime? insuranceExpiry, DateTime? lastServiceDate, DateTime? nextServiceDue, String? condition, List<String>? imageUrls, bool? isPrimary, bool? isActive
});




}
/// @nodoc
class __$UpdateUserVehicleRequestCopyWithImpl<$Res>
    implements _$UpdateUserVehicleRequestCopyWith<$Res> {
  __$UpdateUserVehicleRequestCopyWithImpl(this._self, this._then);

  final _UpdateUserVehicleRequest _self;
  final $Res Function(_UpdateUserVehicleRequest) _then;

/// Create a copy of UpdateUserVehicleRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? color = freezed,Object? vin = freezed,Object? licensePlate = freezed,Object? mileage = freezed,Object? notes = freezed,Object? purchaseDate = freezed,Object? purchasePrice = freezed,Object? insuranceExpiry = freezed,Object? lastServiceDate = freezed,Object? nextServiceDue = freezed,Object? condition = freezed,Object? imageUrls = freezed,Object? isPrimary = freezed,Object? isActive = freezed,}) {
  return _then(_UpdateUserVehicleRequest(
color: freezed == color ? _self.color : color // ignore: cast_nullable_to_non_nullable
as String?,vin: freezed == vin ? _self.vin : vin // ignore: cast_nullable_to_non_nullable
as String?,licensePlate: freezed == licensePlate ? _self.licensePlate : licensePlate // ignore: cast_nullable_to_non_nullable
as String?,mileage: freezed == mileage ? _self.mileage : mileage // ignore: cast_nullable_to_non_nullable
as int?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,purchaseDate: freezed == purchaseDate ? _self.purchaseDate : purchaseDate // ignore: cast_nullable_to_non_nullable
as DateTime?,purchasePrice: freezed == purchasePrice ? _self.purchasePrice : purchasePrice // ignore: cast_nullable_to_non_nullable
as double?,insuranceExpiry: freezed == insuranceExpiry ? _self.insuranceExpiry : insuranceExpiry // ignore: cast_nullable_to_non_nullable
as DateTime?,lastServiceDate: freezed == lastServiceDate ? _self.lastServiceDate : lastServiceDate // ignore: cast_nullable_to_non_nullable
as DateTime?,nextServiceDue: freezed == nextServiceDue ? _self.nextServiceDue : nextServiceDue // ignore: cast_nullable_to_non_nullable
as DateTime?,condition: freezed == condition ? _self.condition : condition // ignore: cast_nullable_to_non_nullable
as String?,imageUrls: freezed == imageUrls ? _self._imageUrls : imageUrls // ignore: cast_nullable_to_non_nullable
as List<String>?,isPrimary: freezed == isPrimary ? _self.isPrimary : isPrimary // ignore: cast_nullable_to_non_nullable
as bool?,isActive: freezed == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}


}

// dart format on
