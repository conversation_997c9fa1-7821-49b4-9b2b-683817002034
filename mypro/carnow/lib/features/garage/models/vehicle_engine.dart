import 'package:freezed_annotation/freezed_annotation.dart';

part 'vehicle_engine.freezed.dart';
part 'vehicle_engine.g.dart';

/// Vehicle Engine - Forever Plan Architecture Compliant
/// Represents engine specifications
@freezed
abstract class VehicleEngine with _$VehicleEngine {
  const factory VehicleEngine({
    required int id,
    required int modelId,
    int? trimId,
    String? engineCode,
    @Default(0) int displacementCc,
    @Default(0) int cylinders,
    @Default(0) int powerHp,
    @Default(0) int torqueNm,
    String? fuelType,
    @Default(false) bool isTurbo,
    String? engineFamily,
    String? valveTrain,
    String? description,
  }) = _VehicleEngine;

  factory VehicleEngine.fromJson(Map<String, dynamic> json) =>
      _$VehicleEngineFromJson(json);
}

/// Vehicle Engine List Response
@freezed
abstract class VehicleEngineListResponse with _$VehicleEngineListResponse {
  const factory VehicleEngineListResponse({
    required List<VehicleEngine> engines,
    required int total,
    @Default(false) bool hasMore,
  }) = _VehicleEngineListResponse;

  factory VehicleEngineListResponse.fromJson(Map<String, dynamic> json) =>
      _$VehicleEngineListResponseFromJson(json);
}
