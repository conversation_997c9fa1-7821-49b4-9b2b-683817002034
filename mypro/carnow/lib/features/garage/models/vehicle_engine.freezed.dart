// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vehicle_engine.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$VehicleEngine {

 int get id; int get modelId; int? get trimId; String? get engineCode; int get displacementCc; int get cylinders; int get powerHp; int get torqueNm; String? get fuelType; bool get isTurbo; String? get engineFamily; String? get valveTrain; String? get description;
/// Create a copy of VehicleEngine
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleEngineCopyWith<VehicleEngine> get copyWith => _$VehicleEngineCopyWithImpl<VehicleEngine>(this as VehicleEngine, _$identity);

  /// Serializes this VehicleEngine to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleEngine&&(identical(other.id, id) || other.id == id)&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.trimId, trimId) || other.trimId == trimId)&&(identical(other.engineCode, engineCode) || other.engineCode == engineCode)&&(identical(other.displacementCc, displacementCc) || other.displacementCc == displacementCc)&&(identical(other.cylinders, cylinders) || other.cylinders == cylinders)&&(identical(other.powerHp, powerHp) || other.powerHp == powerHp)&&(identical(other.torqueNm, torqueNm) || other.torqueNm == torqueNm)&&(identical(other.fuelType, fuelType) || other.fuelType == fuelType)&&(identical(other.isTurbo, isTurbo) || other.isTurbo == isTurbo)&&(identical(other.engineFamily, engineFamily) || other.engineFamily == engineFamily)&&(identical(other.valveTrain, valveTrain) || other.valveTrain == valveTrain)&&(identical(other.description, description) || other.description == description));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,modelId,trimId,engineCode,displacementCc,cylinders,powerHp,torqueNm,fuelType,isTurbo,engineFamily,valveTrain,description);

@override
String toString() {
  return 'VehicleEngine(id: $id, modelId: $modelId, trimId: $trimId, engineCode: $engineCode, displacementCc: $displacementCc, cylinders: $cylinders, powerHp: $powerHp, torqueNm: $torqueNm, fuelType: $fuelType, isTurbo: $isTurbo, engineFamily: $engineFamily, valveTrain: $valveTrain, description: $description)';
}


}

/// @nodoc
abstract mixin class $VehicleEngineCopyWith<$Res>  {
  factory $VehicleEngineCopyWith(VehicleEngine value, $Res Function(VehicleEngine) _then) = _$VehicleEngineCopyWithImpl;
@useResult
$Res call({
 int id, int modelId, int? trimId, String? engineCode, int displacementCc, int cylinders, int powerHp, int torqueNm, String? fuelType, bool isTurbo, String? engineFamily, String? valveTrain, String? description
});




}
/// @nodoc
class _$VehicleEngineCopyWithImpl<$Res>
    implements $VehicleEngineCopyWith<$Res> {
  _$VehicleEngineCopyWithImpl(this._self, this._then);

  final VehicleEngine _self;
  final $Res Function(VehicleEngine) _then;

/// Create a copy of VehicleEngine
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? modelId = null,Object? trimId = freezed,Object? engineCode = freezed,Object? displacementCc = null,Object? cylinders = null,Object? powerHp = null,Object? torqueNm = null,Object? fuelType = freezed,Object? isTurbo = null,Object? engineFamily = freezed,Object? valveTrain = freezed,Object? description = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,modelId: null == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as int,trimId: freezed == trimId ? _self.trimId : trimId // ignore: cast_nullable_to_non_nullable
as int?,engineCode: freezed == engineCode ? _self.engineCode : engineCode // ignore: cast_nullable_to_non_nullable
as String?,displacementCc: null == displacementCc ? _self.displacementCc : displacementCc // ignore: cast_nullable_to_non_nullable
as int,cylinders: null == cylinders ? _self.cylinders : cylinders // ignore: cast_nullable_to_non_nullable
as int,powerHp: null == powerHp ? _self.powerHp : powerHp // ignore: cast_nullable_to_non_nullable
as int,torqueNm: null == torqueNm ? _self.torqueNm : torqueNm // ignore: cast_nullable_to_non_nullable
as int,fuelType: freezed == fuelType ? _self.fuelType : fuelType // ignore: cast_nullable_to_non_nullable
as String?,isTurbo: null == isTurbo ? _self.isTurbo : isTurbo // ignore: cast_nullable_to_non_nullable
as bool,engineFamily: freezed == engineFamily ? _self.engineFamily : engineFamily // ignore: cast_nullable_to_non_nullable
as String?,valveTrain: freezed == valveTrain ? _self.valveTrain : valveTrain // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleEngine].
extension VehicleEnginePatterns on VehicleEngine {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleEngine value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleEngine() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleEngine value)  $default,){
final _that = this;
switch (_that) {
case _VehicleEngine():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleEngine value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleEngine() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  int modelId,  int? trimId,  String? engineCode,  int displacementCc,  int cylinders,  int powerHp,  int torqueNm,  String? fuelType,  bool isTurbo,  String? engineFamily,  String? valveTrain,  String? description)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleEngine() when $default != null:
return $default(_that.id,_that.modelId,_that.trimId,_that.engineCode,_that.displacementCc,_that.cylinders,_that.powerHp,_that.torqueNm,_that.fuelType,_that.isTurbo,_that.engineFamily,_that.valveTrain,_that.description);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  int modelId,  int? trimId,  String? engineCode,  int displacementCc,  int cylinders,  int powerHp,  int torqueNm,  String? fuelType,  bool isTurbo,  String? engineFamily,  String? valveTrain,  String? description)  $default,) {final _that = this;
switch (_that) {
case _VehicleEngine():
return $default(_that.id,_that.modelId,_that.trimId,_that.engineCode,_that.displacementCc,_that.cylinders,_that.powerHp,_that.torqueNm,_that.fuelType,_that.isTurbo,_that.engineFamily,_that.valveTrain,_that.description);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  int modelId,  int? trimId,  String? engineCode,  int displacementCc,  int cylinders,  int powerHp,  int torqueNm,  String? fuelType,  bool isTurbo,  String? engineFamily,  String? valveTrain,  String? description)?  $default,) {final _that = this;
switch (_that) {
case _VehicleEngine() when $default != null:
return $default(_that.id,_that.modelId,_that.trimId,_that.engineCode,_that.displacementCc,_that.cylinders,_that.powerHp,_that.torqueNm,_that.fuelType,_that.isTurbo,_that.engineFamily,_that.valveTrain,_that.description);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleEngine implements VehicleEngine {
  const _VehicleEngine({required this.id, required this.modelId, this.trimId, this.engineCode, this.displacementCc = 0, this.cylinders = 0, this.powerHp = 0, this.torqueNm = 0, this.fuelType, this.isTurbo = false, this.engineFamily, this.valveTrain, this.description});
  factory _VehicleEngine.fromJson(Map<String, dynamic> json) => _$VehicleEngineFromJson(json);

@override final  int id;
@override final  int modelId;
@override final  int? trimId;
@override final  String? engineCode;
@override@JsonKey() final  int displacementCc;
@override@JsonKey() final  int cylinders;
@override@JsonKey() final  int powerHp;
@override@JsonKey() final  int torqueNm;
@override final  String? fuelType;
@override@JsonKey() final  bool isTurbo;
@override final  String? engineFamily;
@override final  String? valveTrain;
@override final  String? description;

/// Create a copy of VehicleEngine
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleEngineCopyWith<_VehicleEngine> get copyWith => __$VehicleEngineCopyWithImpl<_VehicleEngine>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleEngineToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleEngine&&(identical(other.id, id) || other.id == id)&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.trimId, trimId) || other.trimId == trimId)&&(identical(other.engineCode, engineCode) || other.engineCode == engineCode)&&(identical(other.displacementCc, displacementCc) || other.displacementCc == displacementCc)&&(identical(other.cylinders, cylinders) || other.cylinders == cylinders)&&(identical(other.powerHp, powerHp) || other.powerHp == powerHp)&&(identical(other.torqueNm, torqueNm) || other.torqueNm == torqueNm)&&(identical(other.fuelType, fuelType) || other.fuelType == fuelType)&&(identical(other.isTurbo, isTurbo) || other.isTurbo == isTurbo)&&(identical(other.engineFamily, engineFamily) || other.engineFamily == engineFamily)&&(identical(other.valveTrain, valveTrain) || other.valveTrain == valveTrain)&&(identical(other.description, description) || other.description == description));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,modelId,trimId,engineCode,displacementCc,cylinders,powerHp,torqueNm,fuelType,isTurbo,engineFamily,valveTrain,description);

@override
String toString() {
  return 'VehicleEngine(id: $id, modelId: $modelId, trimId: $trimId, engineCode: $engineCode, displacementCc: $displacementCc, cylinders: $cylinders, powerHp: $powerHp, torqueNm: $torqueNm, fuelType: $fuelType, isTurbo: $isTurbo, engineFamily: $engineFamily, valveTrain: $valveTrain, description: $description)';
}


}

/// @nodoc
abstract mixin class _$VehicleEngineCopyWith<$Res> implements $VehicleEngineCopyWith<$Res> {
  factory _$VehicleEngineCopyWith(_VehicleEngine value, $Res Function(_VehicleEngine) _then) = __$VehicleEngineCopyWithImpl;
@override @useResult
$Res call({
 int id, int modelId, int? trimId, String? engineCode, int displacementCc, int cylinders, int powerHp, int torqueNm, String? fuelType, bool isTurbo, String? engineFamily, String? valveTrain, String? description
});




}
/// @nodoc
class __$VehicleEngineCopyWithImpl<$Res>
    implements _$VehicleEngineCopyWith<$Res> {
  __$VehicleEngineCopyWithImpl(this._self, this._then);

  final _VehicleEngine _self;
  final $Res Function(_VehicleEngine) _then;

/// Create a copy of VehicleEngine
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? modelId = null,Object? trimId = freezed,Object? engineCode = freezed,Object? displacementCc = null,Object? cylinders = null,Object? powerHp = null,Object? torqueNm = null,Object? fuelType = freezed,Object? isTurbo = null,Object? engineFamily = freezed,Object? valveTrain = freezed,Object? description = freezed,}) {
  return _then(_VehicleEngine(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,modelId: null == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as int,trimId: freezed == trimId ? _self.trimId : trimId // ignore: cast_nullable_to_non_nullable
as int?,engineCode: freezed == engineCode ? _self.engineCode : engineCode // ignore: cast_nullable_to_non_nullable
as String?,displacementCc: null == displacementCc ? _self.displacementCc : displacementCc // ignore: cast_nullable_to_non_nullable
as int,cylinders: null == cylinders ? _self.cylinders : cylinders // ignore: cast_nullable_to_non_nullable
as int,powerHp: null == powerHp ? _self.powerHp : powerHp // ignore: cast_nullable_to_non_nullable
as int,torqueNm: null == torqueNm ? _self.torqueNm : torqueNm // ignore: cast_nullable_to_non_nullable
as int,fuelType: freezed == fuelType ? _self.fuelType : fuelType // ignore: cast_nullable_to_non_nullable
as String?,isTurbo: null == isTurbo ? _self.isTurbo : isTurbo // ignore: cast_nullable_to_non_nullable
as bool,engineFamily: freezed == engineFamily ? _self.engineFamily : engineFamily // ignore: cast_nullable_to_non_nullable
as String?,valveTrain: freezed == valveTrain ? _self.valveTrain : valveTrain // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$VehicleEngineListResponse {

 List<VehicleEngine> get engines; int get total; bool get hasMore;
/// Create a copy of VehicleEngineListResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleEngineListResponseCopyWith<VehicleEngineListResponse> get copyWith => _$VehicleEngineListResponseCopyWithImpl<VehicleEngineListResponse>(this as VehicleEngineListResponse, _$identity);

  /// Serializes this VehicleEngineListResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleEngineListResponse&&const DeepCollectionEquality().equals(other.engines, engines)&&(identical(other.total, total) || other.total == total)&&(identical(other.hasMore, hasMore) || other.hasMore == hasMore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(engines),total,hasMore);

@override
String toString() {
  return 'VehicleEngineListResponse(engines: $engines, total: $total, hasMore: $hasMore)';
}


}

/// @nodoc
abstract mixin class $VehicleEngineListResponseCopyWith<$Res>  {
  factory $VehicleEngineListResponseCopyWith(VehicleEngineListResponse value, $Res Function(VehicleEngineListResponse) _then) = _$VehicleEngineListResponseCopyWithImpl;
@useResult
$Res call({
 List<VehicleEngine> engines, int total, bool hasMore
});




}
/// @nodoc
class _$VehicleEngineListResponseCopyWithImpl<$Res>
    implements $VehicleEngineListResponseCopyWith<$Res> {
  _$VehicleEngineListResponseCopyWithImpl(this._self, this._then);

  final VehicleEngineListResponse _self;
  final $Res Function(VehicleEngineListResponse) _then;

/// Create a copy of VehicleEngineListResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? engines = null,Object? total = null,Object? hasMore = null,}) {
  return _then(_self.copyWith(
engines: null == engines ? _self.engines : engines // ignore: cast_nullable_to_non_nullable
as List<VehicleEngine>,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int,hasMore: null == hasMore ? _self.hasMore : hasMore // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleEngineListResponse].
extension VehicleEngineListResponsePatterns on VehicleEngineListResponse {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleEngineListResponse value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleEngineListResponse() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleEngineListResponse value)  $default,){
final _that = this;
switch (_that) {
case _VehicleEngineListResponse():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleEngineListResponse value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleEngineListResponse() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<VehicleEngine> engines,  int total,  bool hasMore)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleEngineListResponse() when $default != null:
return $default(_that.engines,_that.total,_that.hasMore);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<VehicleEngine> engines,  int total,  bool hasMore)  $default,) {final _that = this;
switch (_that) {
case _VehicleEngineListResponse():
return $default(_that.engines,_that.total,_that.hasMore);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<VehicleEngine> engines,  int total,  bool hasMore)?  $default,) {final _that = this;
switch (_that) {
case _VehicleEngineListResponse() when $default != null:
return $default(_that.engines,_that.total,_that.hasMore);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleEngineListResponse implements VehicleEngineListResponse {
  const _VehicleEngineListResponse({required final  List<VehicleEngine> engines, required this.total, this.hasMore = false}): _engines = engines;
  factory _VehicleEngineListResponse.fromJson(Map<String, dynamic> json) => _$VehicleEngineListResponseFromJson(json);

 final  List<VehicleEngine> _engines;
@override List<VehicleEngine> get engines {
  if (_engines is EqualUnmodifiableListView) return _engines;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_engines);
}

@override final  int total;
@override@JsonKey() final  bool hasMore;

/// Create a copy of VehicleEngineListResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleEngineListResponseCopyWith<_VehicleEngineListResponse> get copyWith => __$VehicleEngineListResponseCopyWithImpl<_VehicleEngineListResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleEngineListResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleEngineListResponse&&const DeepCollectionEquality().equals(other._engines, _engines)&&(identical(other.total, total) || other.total == total)&&(identical(other.hasMore, hasMore) || other.hasMore == hasMore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_engines),total,hasMore);

@override
String toString() {
  return 'VehicleEngineListResponse(engines: $engines, total: $total, hasMore: $hasMore)';
}


}

/// @nodoc
abstract mixin class _$VehicleEngineListResponseCopyWith<$Res> implements $VehicleEngineListResponseCopyWith<$Res> {
  factory _$VehicleEngineListResponseCopyWith(_VehicleEngineListResponse value, $Res Function(_VehicleEngineListResponse) _then) = __$VehicleEngineListResponseCopyWithImpl;
@override @useResult
$Res call({
 List<VehicleEngine> engines, int total, bool hasMore
});




}
/// @nodoc
class __$VehicleEngineListResponseCopyWithImpl<$Res>
    implements _$VehicleEngineListResponseCopyWith<$Res> {
  __$VehicleEngineListResponseCopyWithImpl(this._self, this._then);

  final _VehicleEngineListResponse _self;
  final $Res Function(_VehicleEngineListResponse) _then;

/// Create a copy of VehicleEngineListResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? engines = null,Object? total = null,Object? hasMore = null,}) {
  return _then(_VehicleEngineListResponse(
engines: null == engines ? _self._engines : engines // ignore: cast_nullable_to_non_nullable
as List<VehicleEngine>,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int,hasMore: null == hasMore ? _self.hasMore : hasMore // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
