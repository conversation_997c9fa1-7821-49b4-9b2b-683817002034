// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_engine.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_VehicleEngine _$VehicleEngineFromJson(Map<String, dynamic> json) =>
    _VehicleEngine(
      id: (json['id'] as num).toInt(),
      modelId: (json['modelId'] as num).toInt(),
      trimId: (json['trimId'] as num?)?.toInt(),
      engineCode: json['engineCode'] as String?,
      displacementCc: (json['displacementCc'] as num?)?.toInt() ?? 0,
      cylinders: (json['cylinders'] as num?)?.toInt() ?? 0,
      powerHp: (json['powerHp'] as num?)?.toInt() ?? 0,
      torqueNm: (json['torqueNm'] as num?)?.toInt() ?? 0,
      fuelType: json['fuelType'] as String?,
      isTurbo: json['isTurbo'] as bool? ?? false,
      engineFamily: json['engineFamily'] as String?,
      valveTrain: json['valveTrain'] as String?,
      description: json['description'] as String?,
    );

Map<String, dynamic> _$VehicleEngineToJson(_VehicleEngine instance) =>
    <String, dynamic>{
      'id': instance.id,
      'modelId': instance.modelId,
      'trimId': instance.trimId,
      'engineCode': instance.engineCode,
      'displacementCc': instance.displacementCc,
      'cylinders': instance.cylinders,
      'powerHp': instance.powerHp,
      'torqueNm': instance.torqueNm,
      'fuelType': instance.fuelType,
      'isTurbo': instance.isTurbo,
      'engineFamily': instance.engineFamily,
      'valveTrain': instance.valveTrain,
      'description': instance.description,
    };

_VehicleEngineListResponse _$VehicleEngineListResponseFromJson(
  Map<String, dynamic> json,
) => _VehicleEngineListResponse(
  engines: (json['engines'] as List<dynamic>)
      .map((e) => VehicleEngine.fromJson(e as Map<String, dynamic>))
      .toList(),
  total: (json['total'] as num).toInt(),
  hasMore: json['hasMore'] as bool? ?? false,
);

Map<String, dynamic> _$VehicleEngineListResponseToJson(
  _VehicleEngineListResponse instance,
) => <String, dynamic>{
  'engines': instance.engines,
  'total': instance.total,
  'hasMore': instance.hasMore,
};
