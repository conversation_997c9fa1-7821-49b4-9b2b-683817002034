import 'package:freezed_annotation/freezed_annotation.dart';

part 'vehicle_make.freezed.dart';
part 'vehicle_make.g.dart';

/// Vehicle Make Model - Forever Plan Architecture Compliant
/// Represents a vehicle manufacturer (BMW, Toyota, etc.)
@freezed
abstract class VehicleMake with _$VehicleMake {
  const factory VehicleMake({
    required int id,
    required String name,
    String? country,
    String? logoUrl,
    @Default(true) bool isActive,
    @Default(0) int modelsCount,
  }) = _VehicleMake;

  factory VehicleMake.fromJson(Map<String, dynamic> json) =>
      _$VehicleMakeFromJson(json);
}

/// Vehicle Make List Response
@freezed
abstract class VehicleMakeListResponse with _$VehicleMakeListResponse {
  const factory VehicleMakeListResponse({
    required List<VehicleMake> makes,
    required int total,
    @Default(false) bool hasMore,
  }) = _VehicleMakeListResponse;

  factory VehicleMakeListResponse.fromJson(Map<String, dynamic> json) =>
      _$VehicleMakeListResponseFromJson(json);
}
