// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vehicle_make.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$VehicleMake {

 int get id; String get name; String? get country; String? get logoUrl; bool get isActive; int get modelsCount;
/// Create a copy of VehicleMake
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleMakeCopyWith<VehicleMake> get copyWith => _$VehicleMakeCopyWithImpl<VehicleMake>(this as VehicleMake, _$identity);

  /// Serializes this VehicleMake to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleMake&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.country, country) || other.country == country)&&(identical(other.logoUrl, logoUrl) || other.logoUrl == logoUrl)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.modelsCount, modelsCount) || other.modelsCount == modelsCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,country,logoUrl,isActive,modelsCount);

@override
String toString() {
  return 'VehicleMake(id: $id, name: $name, country: $country, logoUrl: $logoUrl, isActive: $isActive, modelsCount: $modelsCount)';
}


}

/// @nodoc
abstract mixin class $VehicleMakeCopyWith<$Res>  {
  factory $VehicleMakeCopyWith(VehicleMake value, $Res Function(VehicleMake) _then) = _$VehicleMakeCopyWithImpl;
@useResult
$Res call({
 int id, String name, String? country, String? logoUrl, bool isActive, int modelsCount
});




}
/// @nodoc
class _$VehicleMakeCopyWithImpl<$Res>
    implements $VehicleMakeCopyWith<$Res> {
  _$VehicleMakeCopyWithImpl(this._self, this._then);

  final VehicleMake _self;
  final $Res Function(VehicleMake) _then;

/// Create a copy of VehicleMake
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? country = freezed,Object? logoUrl = freezed,Object? isActive = null,Object? modelsCount = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,country: freezed == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String?,logoUrl: freezed == logoUrl ? _self.logoUrl : logoUrl // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,modelsCount: null == modelsCount ? _self.modelsCount : modelsCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleMake].
extension VehicleMakePatterns on VehicleMake {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleMake value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleMake() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleMake value)  $default,){
final _that = this;
switch (_that) {
case _VehicleMake():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleMake value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleMake() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String name,  String? country,  String? logoUrl,  bool isActive,  int modelsCount)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleMake() when $default != null:
return $default(_that.id,_that.name,_that.country,_that.logoUrl,_that.isActive,_that.modelsCount);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String name,  String? country,  String? logoUrl,  bool isActive,  int modelsCount)  $default,) {final _that = this;
switch (_that) {
case _VehicleMake():
return $default(_that.id,_that.name,_that.country,_that.logoUrl,_that.isActive,_that.modelsCount);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String name,  String? country,  String? logoUrl,  bool isActive,  int modelsCount)?  $default,) {final _that = this;
switch (_that) {
case _VehicleMake() when $default != null:
return $default(_that.id,_that.name,_that.country,_that.logoUrl,_that.isActive,_that.modelsCount);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleMake implements VehicleMake {
  const _VehicleMake({required this.id, required this.name, this.country, this.logoUrl, this.isActive = true, this.modelsCount = 0});
  factory _VehicleMake.fromJson(Map<String, dynamic> json) => _$VehicleMakeFromJson(json);

@override final  int id;
@override final  String name;
@override final  String? country;
@override final  String? logoUrl;
@override@JsonKey() final  bool isActive;
@override@JsonKey() final  int modelsCount;

/// Create a copy of VehicleMake
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleMakeCopyWith<_VehicleMake> get copyWith => __$VehicleMakeCopyWithImpl<_VehicleMake>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleMakeToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleMake&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.country, country) || other.country == country)&&(identical(other.logoUrl, logoUrl) || other.logoUrl == logoUrl)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.modelsCount, modelsCount) || other.modelsCount == modelsCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,country,logoUrl,isActive,modelsCount);

@override
String toString() {
  return 'VehicleMake(id: $id, name: $name, country: $country, logoUrl: $logoUrl, isActive: $isActive, modelsCount: $modelsCount)';
}


}

/// @nodoc
abstract mixin class _$VehicleMakeCopyWith<$Res> implements $VehicleMakeCopyWith<$Res> {
  factory _$VehicleMakeCopyWith(_VehicleMake value, $Res Function(_VehicleMake) _then) = __$VehicleMakeCopyWithImpl;
@override @useResult
$Res call({
 int id, String name, String? country, String? logoUrl, bool isActive, int modelsCount
});




}
/// @nodoc
class __$VehicleMakeCopyWithImpl<$Res>
    implements _$VehicleMakeCopyWith<$Res> {
  __$VehicleMakeCopyWithImpl(this._self, this._then);

  final _VehicleMake _self;
  final $Res Function(_VehicleMake) _then;

/// Create a copy of VehicleMake
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? country = freezed,Object? logoUrl = freezed,Object? isActive = null,Object? modelsCount = null,}) {
  return _then(_VehicleMake(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,country: freezed == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String?,logoUrl: freezed == logoUrl ? _self.logoUrl : logoUrl // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,modelsCount: null == modelsCount ? _self.modelsCount : modelsCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$VehicleMakeListResponse {

 List<VehicleMake> get makes; int get total; bool get hasMore;
/// Create a copy of VehicleMakeListResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleMakeListResponseCopyWith<VehicleMakeListResponse> get copyWith => _$VehicleMakeListResponseCopyWithImpl<VehicleMakeListResponse>(this as VehicleMakeListResponse, _$identity);

  /// Serializes this VehicleMakeListResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleMakeListResponse&&const DeepCollectionEquality().equals(other.makes, makes)&&(identical(other.total, total) || other.total == total)&&(identical(other.hasMore, hasMore) || other.hasMore == hasMore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(makes),total,hasMore);

@override
String toString() {
  return 'VehicleMakeListResponse(makes: $makes, total: $total, hasMore: $hasMore)';
}


}

/// @nodoc
abstract mixin class $VehicleMakeListResponseCopyWith<$Res>  {
  factory $VehicleMakeListResponseCopyWith(VehicleMakeListResponse value, $Res Function(VehicleMakeListResponse) _then) = _$VehicleMakeListResponseCopyWithImpl;
@useResult
$Res call({
 List<VehicleMake> makes, int total, bool hasMore
});




}
/// @nodoc
class _$VehicleMakeListResponseCopyWithImpl<$Res>
    implements $VehicleMakeListResponseCopyWith<$Res> {
  _$VehicleMakeListResponseCopyWithImpl(this._self, this._then);

  final VehicleMakeListResponse _self;
  final $Res Function(VehicleMakeListResponse) _then;

/// Create a copy of VehicleMakeListResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? makes = null,Object? total = null,Object? hasMore = null,}) {
  return _then(_self.copyWith(
makes: null == makes ? _self.makes : makes // ignore: cast_nullable_to_non_nullable
as List<VehicleMake>,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int,hasMore: null == hasMore ? _self.hasMore : hasMore // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleMakeListResponse].
extension VehicleMakeListResponsePatterns on VehicleMakeListResponse {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleMakeListResponse value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleMakeListResponse() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleMakeListResponse value)  $default,){
final _that = this;
switch (_that) {
case _VehicleMakeListResponse():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleMakeListResponse value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleMakeListResponse() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<VehicleMake> makes,  int total,  bool hasMore)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleMakeListResponse() when $default != null:
return $default(_that.makes,_that.total,_that.hasMore);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<VehicleMake> makes,  int total,  bool hasMore)  $default,) {final _that = this;
switch (_that) {
case _VehicleMakeListResponse():
return $default(_that.makes,_that.total,_that.hasMore);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<VehicleMake> makes,  int total,  bool hasMore)?  $default,) {final _that = this;
switch (_that) {
case _VehicleMakeListResponse() when $default != null:
return $default(_that.makes,_that.total,_that.hasMore);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleMakeListResponse implements VehicleMakeListResponse {
  const _VehicleMakeListResponse({required final  List<VehicleMake> makes, required this.total, this.hasMore = false}): _makes = makes;
  factory _VehicleMakeListResponse.fromJson(Map<String, dynamic> json) => _$VehicleMakeListResponseFromJson(json);

 final  List<VehicleMake> _makes;
@override List<VehicleMake> get makes {
  if (_makes is EqualUnmodifiableListView) return _makes;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_makes);
}

@override final  int total;
@override@JsonKey() final  bool hasMore;

/// Create a copy of VehicleMakeListResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleMakeListResponseCopyWith<_VehicleMakeListResponse> get copyWith => __$VehicleMakeListResponseCopyWithImpl<_VehicleMakeListResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleMakeListResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleMakeListResponse&&const DeepCollectionEquality().equals(other._makes, _makes)&&(identical(other.total, total) || other.total == total)&&(identical(other.hasMore, hasMore) || other.hasMore == hasMore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_makes),total,hasMore);

@override
String toString() {
  return 'VehicleMakeListResponse(makes: $makes, total: $total, hasMore: $hasMore)';
}


}

/// @nodoc
abstract mixin class _$VehicleMakeListResponseCopyWith<$Res> implements $VehicleMakeListResponseCopyWith<$Res> {
  factory _$VehicleMakeListResponseCopyWith(_VehicleMakeListResponse value, $Res Function(_VehicleMakeListResponse) _then) = __$VehicleMakeListResponseCopyWithImpl;
@override @useResult
$Res call({
 List<VehicleMake> makes, int total, bool hasMore
});




}
/// @nodoc
class __$VehicleMakeListResponseCopyWithImpl<$Res>
    implements _$VehicleMakeListResponseCopyWith<$Res> {
  __$VehicleMakeListResponseCopyWithImpl(this._self, this._then);

  final _VehicleMakeListResponse _self;
  final $Res Function(_VehicleMakeListResponse) _then;

/// Create a copy of VehicleMakeListResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? makes = null,Object? total = null,Object? hasMore = null,}) {
  return _then(_VehicleMakeListResponse(
makes: null == makes ? _self._makes : makes // ignore: cast_nullable_to_non_nullable
as List<VehicleMake>,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int,hasMore: null == hasMore ? _self.hasMore : hasMore // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
