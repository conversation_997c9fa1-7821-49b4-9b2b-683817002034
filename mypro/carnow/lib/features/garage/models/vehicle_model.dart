import 'package:freezed_annotation/freezed_annotation.dart';

part 'vehicle_model.freezed.dart';
part 'vehicle_model.g.dart';

/// Vehicle Model - Forever Plan Architecture Compliant
/// Represents a specific vehicle model (335i, Camry, etc.)
@freezed
abstract class VehicleModel with _$VehicleModel {
  const factory VehicleModel({
    required int id,
    required int makeId,
    required String name,
    String? generation,
    String? bodyType,
    String? fuelType,
    int? yearStart,
    int? yearEnd,
    @Default(true) bool isCurrent,
    String? makeName,
    @Default(0) int yearsCount,
    @Default(0) int trimsCount,
  }) = _VehicleModel;

  factory VehicleModel.fromJson(Map<String, dynamic> json) =>
      _$VehicleModelFromJson(json);
}

/// Vehicle Model List Response
@freezed
abstract class VehicleModelListResponse with _$VehicleModelListResponse {
  const factory VehicleModelListResponse({
    required List<VehicleModel> models,
    required int total,
    @Default(false) bool hasMore,
  }) = _VehicleModelListResponse;

  factory VehicleModelListResponse.fromJson(Map<String, dynamic> json) =>
      _$VehicleModelListResponseFromJson(json);
}
