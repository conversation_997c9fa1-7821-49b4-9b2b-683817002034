// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vehicle_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$VehicleModel {

 int get id; int get makeId; String get name; String? get generation; String? get bodyType; String? get fuelType; int? get yearStart; int? get yearEnd; bool get isCurrent; String? get makeName; int get yearsCount; int get trimsCount;
/// Create a copy of VehicleModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleModelCopyWith<VehicleModel> get copyWith => _$VehicleModelCopyWithImpl<VehicleModel>(this as VehicleModel, _$identity);

  /// Serializes this VehicleModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleModel&&(identical(other.id, id) || other.id == id)&&(identical(other.makeId, makeId) || other.makeId == makeId)&&(identical(other.name, name) || other.name == name)&&(identical(other.generation, generation) || other.generation == generation)&&(identical(other.bodyType, bodyType) || other.bodyType == bodyType)&&(identical(other.fuelType, fuelType) || other.fuelType == fuelType)&&(identical(other.yearStart, yearStart) || other.yearStart == yearStart)&&(identical(other.yearEnd, yearEnd) || other.yearEnd == yearEnd)&&(identical(other.isCurrent, isCurrent) || other.isCurrent == isCurrent)&&(identical(other.makeName, makeName) || other.makeName == makeName)&&(identical(other.yearsCount, yearsCount) || other.yearsCount == yearsCount)&&(identical(other.trimsCount, trimsCount) || other.trimsCount == trimsCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,makeId,name,generation,bodyType,fuelType,yearStart,yearEnd,isCurrent,makeName,yearsCount,trimsCount);

@override
String toString() {
  return 'VehicleModel(id: $id, makeId: $makeId, name: $name, generation: $generation, bodyType: $bodyType, fuelType: $fuelType, yearStart: $yearStart, yearEnd: $yearEnd, isCurrent: $isCurrent, makeName: $makeName, yearsCount: $yearsCount, trimsCount: $trimsCount)';
}


}

/// @nodoc
abstract mixin class $VehicleModelCopyWith<$Res>  {
  factory $VehicleModelCopyWith(VehicleModel value, $Res Function(VehicleModel) _then) = _$VehicleModelCopyWithImpl;
@useResult
$Res call({
 int id, int makeId, String name, String? generation, String? bodyType, String? fuelType, int? yearStart, int? yearEnd, bool isCurrent, String? makeName, int yearsCount, int trimsCount
});




}
/// @nodoc
class _$VehicleModelCopyWithImpl<$Res>
    implements $VehicleModelCopyWith<$Res> {
  _$VehicleModelCopyWithImpl(this._self, this._then);

  final VehicleModel _self;
  final $Res Function(VehicleModel) _then;

/// Create a copy of VehicleModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? makeId = null,Object? name = null,Object? generation = freezed,Object? bodyType = freezed,Object? fuelType = freezed,Object? yearStart = freezed,Object? yearEnd = freezed,Object? isCurrent = null,Object? makeName = freezed,Object? yearsCount = null,Object? trimsCount = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,makeId: null == makeId ? _self.makeId : makeId // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,generation: freezed == generation ? _self.generation : generation // ignore: cast_nullable_to_non_nullable
as String?,bodyType: freezed == bodyType ? _self.bodyType : bodyType // ignore: cast_nullable_to_non_nullable
as String?,fuelType: freezed == fuelType ? _self.fuelType : fuelType // ignore: cast_nullable_to_non_nullable
as String?,yearStart: freezed == yearStart ? _self.yearStart : yearStart // ignore: cast_nullable_to_non_nullable
as int?,yearEnd: freezed == yearEnd ? _self.yearEnd : yearEnd // ignore: cast_nullable_to_non_nullable
as int?,isCurrent: null == isCurrent ? _self.isCurrent : isCurrent // ignore: cast_nullable_to_non_nullable
as bool,makeName: freezed == makeName ? _self.makeName : makeName // ignore: cast_nullable_to_non_nullable
as String?,yearsCount: null == yearsCount ? _self.yearsCount : yearsCount // ignore: cast_nullable_to_non_nullable
as int,trimsCount: null == trimsCount ? _self.trimsCount : trimsCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleModel].
extension VehicleModelPatterns on VehicleModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleModel value)  $default,){
final _that = this;
switch (_that) {
case _VehicleModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleModel value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  int makeId,  String name,  String? generation,  String? bodyType,  String? fuelType,  int? yearStart,  int? yearEnd,  bool isCurrent,  String? makeName,  int yearsCount,  int trimsCount)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleModel() when $default != null:
return $default(_that.id,_that.makeId,_that.name,_that.generation,_that.bodyType,_that.fuelType,_that.yearStart,_that.yearEnd,_that.isCurrent,_that.makeName,_that.yearsCount,_that.trimsCount);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  int makeId,  String name,  String? generation,  String? bodyType,  String? fuelType,  int? yearStart,  int? yearEnd,  bool isCurrent,  String? makeName,  int yearsCount,  int trimsCount)  $default,) {final _that = this;
switch (_that) {
case _VehicleModel():
return $default(_that.id,_that.makeId,_that.name,_that.generation,_that.bodyType,_that.fuelType,_that.yearStart,_that.yearEnd,_that.isCurrent,_that.makeName,_that.yearsCount,_that.trimsCount);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  int makeId,  String name,  String? generation,  String? bodyType,  String? fuelType,  int? yearStart,  int? yearEnd,  bool isCurrent,  String? makeName,  int yearsCount,  int trimsCount)?  $default,) {final _that = this;
switch (_that) {
case _VehicleModel() when $default != null:
return $default(_that.id,_that.makeId,_that.name,_that.generation,_that.bodyType,_that.fuelType,_that.yearStart,_that.yearEnd,_that.isCurrent,_that.makeName,_that.yearsCount,_that.trimsCount);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleModel implements VehicleModel {
  const _VehicleModel({required this.id, required this.makeId, required this.name, this.generation, this.bodyType, this.fuelType, this.yearStart, this.yearEnd, this.isCurrent = true, this.makeName, this.yearsCount = 0, this.trimsCount = 0});
  factory _VehicleModel.fromJson(Map<String, dynamic> json) => _$VehicleModelFromJson(json);

@override final  int id;
@override final  int makeId;
@override final  String name;
@override final  String? generation;
@override final  String? bodyType;
@override final  String? fuelType;
@override final  int? yearStart;
@override final  int? yearEnd;
@override@JsonKey() final  bool isCurrent;
@override final  String? makeName;
@override@JsonKey() final  int yearsCount;
@override@JsonKey() final  int trimsCount;

/// Create a copy of VehicleModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleModelCopyWith<_VehicleModel> get copyWith => __$VehicleModelCopyWithImpl<_VehicleModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleModel&&(identical(other.id, id) || other.id == id)&&(identical(other.makeId, makeId) || other.makeId == makeId)&&(identical(other.name, name) || other.name == name)&&(identical(other.generation, generation) || other.generation == generation)&&(identical(other.bodyType, bodyType) || other.bodyType == bodyType)&&(identical(other.fuelType, fuelType) || other.fuelType == fuelType)&&(identical(other.yearStart, yearStart) || other.yearStart == yearStart)&&(identical(other.yearEnd, yearEnd) || other.yearEnd == yearEnd)&&(identical(other.isCurrent, isCurrent) || other.isCurrent == isCurrent)&&(identical(other.makeName, makeName) || other.makeName == makeName)&&(identical(other.yearsCount, yearsCount) || other.yearsCount == yearsCount)&&(identical(other.trimsCount, trimsCount) || other.trimsCount == trimsCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,makeId,name,generation,bodyType,fuelType,yearStart,yearEnd,isCurrent,makeName,yearsCount,trimsCount);

@override
String toString() {
  return 'VehicleModel(id: $id, makeId: $makeId, name: $name, generation: $generation, bodyType: $bodyType, fuelType: $fuelType, yearStart: $yearStart, yearEnd: $yearEnd, isCurrent: $isCurrent, makeName: $makeName, yearsCount: $yearsCount, trimsCount: $trimsCount)';
}


}

/// @nodoc
abstract mixin class _$VehicleModelCopyWith<$Res> implements $VehicleModelCopyWith<$Res> {
  factory _$VehicleModelCopyWith(_VehicleModel value, $Res Function(_VehicleModel) _then) = __$VehicleModelCopyWithImpl;
@override @useResult
$Res call({
 int id, int makeId, String name, String? generation, String? bodyType, String? fuelType, int? yearStart, int? yearEnd, bool isCurrent, String? makeName, int yearsCount, int trimsCount
});




}
/// @nodoc
class __$VehicleModelCopyWithImpl<$Res>
    implements _$VehicleModelCopyWith<$Res> {
  __$VehicleModelCopyWithImpl(this._self, this._then);

  final _VehicleModel _self;
  final $Res Function(_VehicleModel) _then;

/// Create a copy of VehicleModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? makeId = null,Object? name = null,Object? generation = freezed,Object? bodyType = freezed,Object? fuelType = freezed,Object? yearStart = freezed,Object? yearEnd = freezed,Object? isCurrent = null,Object? makeName = freezed,Object? yearsCount = null,Object? trimsCount = null,}) {
  return _then(_VehicleModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,makeId: null == makeId ? _self.makeId : makeId // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,generation: freezed == generation ? _self.generation : generation // ignore: cast_nullable_to_non_nullable
as String?,bodyType: freezed == bodyType ? _self.bodyType : bodyType // ignore: cast_nullable_to_non_nullable
as String?,fuelType: freezed == fuelType ? _self.fuelType : fuelType // ignore: cast_nullable_to_non_nullable
as String?,yearStart: freezed == yearStart ? _self.yearStart : yearStart // ignore: cast_nullable_to_non_nullable
as int?,yearEnd: freezed == yearEnd ? _self.yearEnd : yearEnd // ignore: cast_nullable_to_non_nullable
as int?,isCurrent: null == isCurrent ? _self.isCurrent : isCurrent // ignore: cast_nullable_to_non_nullable
as bool,makeName: freezed == makeName ? _self.makeName : makeName // ignore: cast_nullable_to_non_nullable
as String?,yearsCount: null == yearsCount ? _self.yearsCount : yearsCount // ignore: cast_nullable_to_non_nullable
as int,trimsCount: null == trimsCount ? _self.trimsCount : trimsCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$VehicleModelListResponse {

 List<VehicleModel> get models; int get total; bool get hasMore;
/// Create a copy of VehicleModelListResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleModelListResponseCopyWith<VehicleModelListResponse> get copyWith => _$VehicleModelListResponseCopyWithImpl<VehicleModelListResponse>(this as VehicleModelListResponse, _$identity);

  /// Serializes this VehicleModelListResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleModelListResponse&&const DeepCollectionEquality().equals(other.models, models)&&(identical(other.total, total) || other.total == total)&&(identical(other.hasMore, hasMore) || other.hasMore == hasMore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(models),total,hasMore);

@override
String toString() {
  return 'VehicleModelListResponse(models: $models, total: $total, hasMore: $hasMore)';
}


}

/// @nodoc
abstract mixin class $VehicleModelListResponseCopyWith<$Res>  {
  factory $VehicleModelListResponseCopyWith(VehicleModelListResponse value, $Res Function(VehicleModelListResponse) _then) = _$VehicleModelListResponseCopyWithImpl;
@useResult
$Res call({
 List<VehicleModel> models, int total, bool hasMore
});




}
/// @nodoc
class _$VehicleModelListResponseCopyWithImpl<$Res>
    implements $VehicleModelListResponseCopyWith<$Res> {
  _$VehicleModelListResponseCopyWithImpl(this._self, this._then);

  final VehicleModelListResponse _self;
  final $Res Function(VehicleModelListResponse) _then;

/// Create a copy of VehicleModelListResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? models = null,Object? total = null,Object? hasMore = null,}) {
  return _then(_self.copyWith(
models: null == models ? _self.models : models // ignore: cast_nullable_to_non_nullable
as List<VehicleModel>,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int,hasMore: null == hasMore ? _self.hasMore : hasMore // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleModelListResponse].
extension VehicleModelListResponsePatterns on VehicleModelListResponse {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleModelListResponse value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleModelListResponse() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleModelListResponse value)  $default,){
final _that = this;
switch (_that) {
case _VehicleModelListResponse():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleModelListResponse value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleModelListResponse() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<VehicleModel> models,  int total,  bool hasMore)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleModelListResponse() when $default != null:
return $default(_that.models,_that.total,_that.hasMore);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<VehicleModel> models,  int total,  bool hasMore)  $default,) {final _that = this;
switch (_that) {
case _VehicleModelListResponse():
return $default(_that.models,_that.total,_that.hasMore);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<VehicleModel> models,  int total,  bool hasMore)?  $default,) {final _that = this;
switch (_that) {
case _VehicleModelListResponse() when $default != null:
return $default(_that.models,_that.total,_that.hasMore);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleModelListResponse implements VehicleModelListResponse {
  const _VehicleModelListResponse({required final  List<VehicleModel> models, required this.total, this.hasMore = false}): _models = models;
  factory _VehicleModelListResponse.fromJson(Map<String, dynamic> json) => _$VehicleModelListResponseFromJson(json);

 final  List<VehicleModel> _models;
@override List<VehicleModel> get models {
  if (_models is EqualUnmodifiableListView) return _models;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_models);
}

@override final  int total;
@override@JsonKey() final  bool hasMore;

/// Create a copy of VehicleModelListResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleModelListResponseCopyWith<_VehicleModelListResponse> get copyWith => __$VehicleModelListResponseCopyWithImpl<_VehicleModelListResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleModelListResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleModelListResponse&&const DeepCollectionEquality().equals(other._models, _models)&&(identical(other.total, total) || other.total == total)&&(identical(other.hasMore, hasMore) || other.hasMore == hasMore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_models),total,hasMore);

@override
String toString() {
  return 'VehicleModelListResponse(models: $models, total: $total, hasMore: $hasMore)';
}


}

/// @nodoc
abstract mixin class _$VehicleModelListResponseCopyWith<$Res> implements $VehicleModelListResponseCopyWith<$Res> {
  factory _$VehicleModelListResponseCopyWith(_VehicleModelListResponse value, $Res Function(_VehicleModelListResponse) _then) = __$VehicleModelListResponseCopyWithImpl;
@override @useResult
$Res call({
 List<VehicleModel> models, int total, bool hasMore
});




}
/// @nodoc
class __$VehicleModelListResponseCopyWithImpl<$Res>
    implements _$VehicleModelListResponseCopyWith<$Res> {
  __$VehicleModelListResponseCopyWithImpl(this._self, this._then);

  final _VehicleModelListResponse _self;
  final $Res Function(_VehicleModelListResponse) _then;

/// Create a copy of VehicleModelListResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? models = null,Object? total = null,Object? hasMore = null,}) {
  return _then(_VehicleModelListResponse(
models: null == models ? _self._models : models // ignore: cast_nullable_to_non_nullable
as List<VehicleModel>,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int,hasMore: null == hasMore ? _self.hasMore : hasMore // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
