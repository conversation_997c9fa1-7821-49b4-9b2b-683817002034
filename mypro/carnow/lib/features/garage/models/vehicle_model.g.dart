// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_VehicleModel _$VehicleModelFromJson(Map<String, dynamic> json) =>
    _VehicleModel(
      id: (json['id'] as num).toInt(),
      makeId: (json['makeId'] as num).toInt(),
      name: json['name'] as String,
      generation: json['generation'] as String?,
      bodyType: json['bodyType'] as String?,
      fuelType: json['fuelType'] as String?,
      yearStart: (json['yearStart'] as num?)?.toInt(),
      yearEnd: (json['yearEnd'] as num?)?.toInt(),
      isCurrent: json['isCurrent'] as bool? ?? true,
      makeName: json['makeName'] as String?,
      yearsCount: (json['yearsCount'] as num?)?.toInt() ?? 0,
      trimsCount: (json['trimsCount'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$VehicleModelToJson(_VehicleModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'makeId': instance.makeId,
      'name': instance.name,
      'generation': instance.generation,
      'bodyType': instance.bodyType,
      'fuelType': instance.fuelType,
      'yearStart': instance.yearStart,
      'yearEnd': instance.yearEnd,
      'isCurrent': instance.isCurrent,
      'makeName': instance.makeName,
      'yearsCount': instance.yearsCount,
      'trimsCount': instance.trimsCount,
    };

_VehicleModelListResponse _$VehicleModelListResponseFromJson(
  Map<String, dynamic> json,
) => _VehicleModelListResponse(
  models: (json['models'] as List<dynamic>)
      .map((e) => VehicleModel.fromJson(e as Map<String, dynamic>))
      .toList(),
  total: (json['total'] as num).toInt(),
  hasMore: json['hasMore'] as bool? ?? false,
);

Map<String, dynamic> _$VehicleModelListResponseToJson(
  _VehicleModelListResponse instance,
) => <String, dynamic>{
  'models': instance.models,
  'total': instance.total,
  'hasMore': instance.hasMore,
};
