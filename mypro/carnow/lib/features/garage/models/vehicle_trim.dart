import 'package:freezed_annotation/freezed_annotation.dart';

part 'vehicle_trim.freezed.dart';
part 'vehicle_trim.g.dart';

/// Vehicle Trim - Forever Plan Architecture Compliant
/// Represents trim levels (Base, Sport, Luxury, etc.)
@freezed
abstract class VehicleTrim with _$VehicleTrim {
  const factory VehicleTrim({
    required int id,
    required int modelId,
    required String name,
    String? trimLevel,
    int? doors,
    int? seats,
    String? transmissionType,
    String? driveType,
    @Default(true) bool isCurrent,
    @Default(0) int enginesCount,
  }) = _VehicleTrim;

  factory VehicleTrim.fromJson(Map<String, dynamic> json) =>
      _$VehicleTrimFromJson(json);
}

/// Vehicle Trim List Response
@freezed
abstract class VehicleTrimListResponse with _$VehicleTrimListResponse {
  const factory VehicleTrimListResponse({
    required List<VehicleTrim> trims,
    required int total,
    @Default(false) bool hasMore,
  }) = _VehicleTrimListResponse;

  factory VehicleTrimListResponse.fromJson(Map<String, dynamic> json) =>
      _$VehicleTrimListResponseFromJson(json);
}
