// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vehicle_trim.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$VehicleTrim {

 int get id; int get modelId; String get name; String? get trimLevel; int? get doors; int? get seats; String? get transmissionType; String? get driveType; bool get isCurrent; int get enginesCount;
/// Create a copy of VehicleTrim
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleTrimCopyWith<VehicleTrim> get copyWith => _$VehicleTrimCopyWithImpl<VehicleTrim>(this as VehicleTrim, _$identity);

  /// Serializes this VehicleTrim to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleTrim&&(identical(other.id, id) || other.id == id)&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.name, name) || other.name == name)&&(identical(other.trimLevel, trimLevel) || other.trimLevel == trimLevel)&&(identical(other.doors, doors) || other.doors == doors)&&(identical(other.seats, seats) || other.seats == seats)&&(identical(other.transmissionType, transmissionType) || other.transmissionType == transmissionType)&&(identical(other.driveType, driveType) || other.driveType == driveType)&&(identical(other.isCurrent, isCurrent) || other.isCurrent == isCurrent)&&(identical(other.enginesCount, enginesCount) || other.enginesCount == enginesCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,modelId,name,trimLevel,doors,seats,transmissionType,driveType,isCurrent,enginesCount);

@override
String toString() {
  return 'VehicleTrim(id: $id, modelId: $modelId, name: $name, trimLevel: $trimLevel, doors: $doors, seats: $seats, transmissionType: $transmissionType, driveType: $driveType, isCurrent: $isCurrent, enginesCount: $enginesCount)';
}


}

/// @nodoc
abstract mixin class $VehicleTrimCopyWith<$Res>  {
  factory $VehicleTrimCopyWith(VehicleTrim value, $Res Function(VehicleTrim) _then) = _$VehicleTrimCopyWithImpl;
@useResult
$Res call({
 int id, int modelId, String name, String? trimLevel, int? doors, int? seats, String? transmissionType, String? driveType, bool isCurrent, int enginesCount
});




}
/// @nodoc
class _$VehicleTrimCopyWithImpl<$Res>
    implements $VehicleTrimCopyWith<$Res> {
  _$VehicleTrimCopyWithImpl(this._self, this._then);

  final VehicleTrim _self;
  final $Res Function(VehicleTrim) _then;

/// Create a copy of VehicleTrim
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? modelId = null,Object? name = null,Object? trimLevel = freezed,Object? doors = freezed,Object? seats = freezed,Object? transmissionType = freezed,Object? driveType = freezed,Object? isCurrent = null,Object? enginesCount = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,modelId: null == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,trimLevel: freezed == trimLevel ? _self.trimLevel : trimLevel // ignore: cast_nullable_to_non_nullable
as String?,doors: freezed == doors ? _self.doors : doors // ignore: cast_nullable_to_non_nullable
as int?,seats: freezed == seats ? _self.seats : seats // ignore: cast_nullable_to_non_nullable
as int?,transmissionType: freezed == transmissionType ? _self.transmissionType : transmissionType // ignore: cast_nullable_to_non_nullable
as String?,driveType: freezed == driveType ? _self.driveType : driveType // ignore: cast_nullable_to_non_nullable
as String?,isCurrent: null == isCurrent ? _self.isCurrent : isCurrent // ignore: cast_nullable_to_non_nullable
as bool,enginesCount: null == enginesCount ? _self.enginesCount : enginesCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleTrim].
extension VehicleTrimPatterns on VehicleTrim {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleTrim value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleTrim() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleTrim value)  $default,){
final _that = this;
switch (_that) {
case _VehicleTrim():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleTrim value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleTrim() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  int modelId,  String name,  String? trimLevel,  int? doors,  int? seats,  String? transmissionType,  String? driveType,  bool isCurrent,  int enginesCount)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleTrim() when $default != null:
return $default(_that.id,_that.modelId,_that.name,_that.trimLevel,_that.doors,_that.seats,_that.transmissionType,_that.driveType,_that.isCurrent,_that.enginesCount);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  int modelId,  String name,  String? trimLevel,  int? doors,  int? seats,  String? transmissionType,  String? driveType,  bool isCurrent,  int enginesCount)  $default,) {final _that = this;
switch (_that) {
case _VehicleTrim():
return $default(_that.id,_that.modelId,_that.name,_that.trimLevel,_that.doors,_that.seats,_that.transmissionType,_that.driveType,_that.isCurrent,_that.enginesCount);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  int modelId,  String name,  String? trimLevel,  int? doors,  int? seats,  String? transmissionType,  String? driveType,  bool isCurrent,  int enginesCount)?  $default,) {final _that = this;
switch (_that) {
case _VehicleTrim() when $default != null:
return $default(_that.id,_that.modelId,_that.name,_that.trimLevel,_that.doors,_that.seats,_that.transmissionType,_that.driveType,_that.isCurrent,_that.enginesCount);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleTrim implements VehicleTrim {
  const _VehicleTrim({required this.id, required this.modelId, required this.name, this.trimLevel, this.doors, this.seats, this.transmissionType, this.driveType, this.isCurrent = true, this.enginesCount = 0});
  factory _VehicleTrim.fromJson(Map<String, dynamic> json) => _$VehicleTrimFromJson(json);

@override final  int id;
@override final  int modelId;
@override final  String name;
@override final  String? trimLevel;
@override final  int? doors;
@override final  int? seats;
@override final  String? transmissionType;
@override final  String? driveType;
@override@JsonKey() final  bool isCurrent;
@override@JsonKey() final  int enginesCount;

/// Create a copy of VehicleTrim
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleTrimCopyWith<_VehicleTrim> get copyWith => __$VehicleTrimCopyWithImpl<_VehicleTrim>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleTrimToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleTrim&&(identical(other.id, id) || other.id == id)&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.name, name) || other.name == name)&&(identical(other.trimLevel, trimLevel) || other.trimLevel == trimLevel)&&(identical(other.doors, doors) || other.doors == doors)&&(identical(other.seats, seats) || other.seats == seats)&&(identical(other.transmissionType, transmissionType) || other.transmissionType == transmissionType)&&(identical(other.driveType, driveType) || other.driveType == driveType)&&(identical(other.isCurrent, isCurrent) || other.isCurrent == isCurrent)&&(identical(other.enginesCount, enginesCount) || other.enginesCount == enginesCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,modelId,name,trimLevel,doors,seats,transmissionType,driveType,isCurrent,enginesCount);

@override
String toString() {
  return 'VehicleTrim(id: $id, modelId: $modelId, name: $name, trimLevel: $trimLevel, doors: $doors, seats: $seats, transmissionType: $transmissionType, driveType: $driveType, isCurrent: $isCurrent, enginesCount: $enginesCount)';
}


}

/// @nodoc
abstract mixin class _$VehicleTrimCopyWith<$Res> implements $VehicleTrimCopyWith<$Res> {
  factory _$VehicleTrimCopyWith(_VehicleTrim value, $Res Function(_VehicleTrim) _then) = __$VehicleTrimCopyWithImpl;
@override @useResult
$Res call({
 int id, int modelId, String name, String? trimLevel, int? doors, int? seats, String? transmissionType, String? driveType, bool isCurrent, int enginesCount
});




}
/// @nodoc
class __$VehicleTrimCopyWithImpl<$Res>
    implements _$VehicleTrimCopyWith<$Res> {
  __$VehicleTrimCopyWithImpl(this._self, this._then);

  final _VehicleTrim _self;
  final $Res Function(_VehicleTrim) _then;

/// Create a copy of VehicleTrim
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? modelId = null,Object? name = null,Object? trimLevel = freezed,Object? doors = freezed,Object? seats = freezed,Object? transmissionType = freezed,Object? driveType = freezed,Object? isCurrent = null,Object? enginesCount = null,}) {
  return _then(_VehicleTrim(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,modelId: null == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,trimLevel: freezed == trimLevel ? _self.trimLevel : trimLevel // ignore: cast_nullable_to_non_nullable
as String?,doors: freezed == doors ? _self.doors : doors // ignore: cast_nullable_to_non_nullable
as int?,seats: freezed == seats ? _self.seats : seats // ignore: cast_nullable_to_non_nullable
as int?,transmissionType: freezed == transmissionType ? _self.transmissionType : transmissionType // ignore: cast_nullable_to_non_nullable
as String?,driveType: freezed == driveType ? _self.driveType : driveType // ignore: cast_nullable_to_non_nullable
as String?,isCurrent: null == isCurrent ? _self.isCurrent : isCurrent // ignore: cast_nullable_to_non_nullable
as bool,enginesCount: null == enginesCount ? _self.enginesCount : enginesCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$VehicleTrimListResponse {

 List<VehicleTrim> get trims; int get total; bool get hasMore;
/// Create a copy of VehicleTrimListResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleTrimListResponseCopyWith<VehicleTrimListResponse> get copyWith => _$VehicleTrimListResponseCopyWithImpl<VehicleTrimListResponse>(this as VehicleTrimListResponse, _$identity);

  /// Serializes this VehicleTrimListResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleTrimListResponse&&const DeepCollectionEquality().equals(other.trims, trims)&&(identical(other.total, total) || other.total == total)&&(identical(other.hasMore, hasMore) || other.hasMore == hasMore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(trims),total,hasMore);

@override
String toString() {
  return 'VehicleTrimListResponse(trims: $trims, total: $total, hasMore: $hasMore)';
}


}

/// @nodoc
abstract mixin class $VehicleTrimListResponseCopyWith<$Res>  {
  factory $VehicleTrimListResponseCopyWith(VehicleTrimListResponse value, $Res Function(VehicleTrimListResponse) _then) = _$VehicleTrimListResponseCopyWithImpl;
@useResult
$Res call({
 List<VehicleTrim> trims, int total, bool hasMore
});




}
/// @nodoc
class _$VehicleTrimListResponseCopyWithImpl<$Res>
    implements $VehicleTrimListResponseCopyWith<$Res> {
  _$VehicleTrimListResponseCopyWithImpl(this._self, this._then);

  final VehicleTrimListResponse _self;
  final $Res Function(VehicleTrimListResponse) _then;

/// Create a copy of VehicleTrimListResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? trims = null,Object? total = null,Object? hasMore = null,}) {
  return _then(_self.copyWith(
trims: null == trims ? _self.trims : trims // ignore: cast_nullable_to_non_nullable
as List<VehicleTrim>,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int,hasMore: null == hasMore ? _self.hasMore : hasMore // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleTrimListResponse].
extension VehicleTrimListResponsePatterns on VehicleTrimListResponse {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleTrimListResponse value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleTrimListResponse() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleTrimListResponse value)  $default,){
final _that = this;
switch (_that) {
case _VehicleTrimListResponse():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleTrimListResponse value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleTrimListResponse() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<VehicleTrim> trims,  int total,  bool hasMore)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleTrimListResponse() when $default != null:
return $default(_that.trims,_that.total,_that.hasMore);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<VehicleTrim> trims,  int total,  bool hasMore)  $default,) {final _that = this;
switch (_that) {
case _VehicleTrimListResponse():
return $default(_that.trims,_that.total,_that.hasMore);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<VehicleTrim> trims,  int total,  bool hasMore)?  $default,) {final _that = this;
switch (_that) {
case _VehicleTrimListResponse() when $default != null:
return $default(_that.trims,_that.total,_that.hasMore);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleTrimListResponse implements VehicleTrimListResponse {
  const _VehicleTrimListResponse({required final  List<VehicleTrim> trims, required this.total, this.hasMore = false}): _trims = trims;
  factory _VehicleTrimListResponse.fromJson(Map<String, dynamic> json) => _$VehicleTrimListResponseFromJson(json);

 final  List<VehicleTrim> _trims;
@override List<VehicleTrim> get trims {
  if (_trims is EqualUnmodifiableListView) return _trims;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_trims);
}

@override final  int total;
@override@JsonKey() final  bool hasMore;

/// Create a copy of VehicleTrimListResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleTrimListResponseCopyWith<_VehicleTrimListResponse> get copyWith => __$VehicleTrimListResponseCopyWithImpl<_VehicleTrimListResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleTrimListResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleTrimListResponse&&const DeepCollectionEquality().equals(other._trims, _trims)&&(identical(other.total, total) || other.total == total)&&(identical(other.hasMore, hasMore) || other.hasMore == hasMore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_trims),total,hasMore);

@override
String toString() {
  return 'VehicleTrimListResponse(trims: $trims, total: $total, hasMore: $hasMore)';
}


}

/// @nodoc
abstract mixin class _$VehicleTrimListResponseCopyWith<$Res> implements $VehicleTrimListResponseCopyWith<$Res> {
  factory _$VehicleTrimListResponseCopyWith(_VehicleTrimListResponse value, $Res Function(_VehicleTrimListResponse) _then) = __$VehicleTrimListResponseCopyWithImpl;
@override @useResult
$Res call({
 List<VehicleTrim> trims, int total, bool hasMore
});




}
/// @nodoc
class __$VehicleTrimListResponseCopyWithImpl<$Res>
    implements _$VehicleTrimListResponseCopyWith<$Res> {
  __$VehicleTrimListResponseCopyWithImpl(this._self, this._then);

  final _VehicleTrimListResponse _self;
  final $Res Function(_VehicleTrimListResponse) _then;

/// Create a copy of VehicleTrimListResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? trims = null,Object? total = null,Object? hasMore = null,}) {
  return _then(_VehicleTrimListResponse(
trims: null == trims ? _self._trims : trims // ignore: cast_nullable_to_non_nullable
as List<VehicleTrim>,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int,hasMore: null == hasMore ? _self.hasMore : hasMore // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
