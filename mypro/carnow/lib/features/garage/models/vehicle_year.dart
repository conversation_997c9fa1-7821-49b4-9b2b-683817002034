import 'package:freezed_annotation/freezed_annotation.dart';

part 'vehicle_year.freezed.dart';
part 'vehicle_year.g.dart';

/// Vehicle Year - Forever Plan Architecture Compliant
/// Represents specific years for each model
@freezed
abstract class VehicleYear with _$VehicleYear {
  const factory VehicleYear({
    required int id,
    required int modelId,
    required int year,
    String? modelName,
    String? makeName,
  }) = _VehicleYear;

  factory VehicleYear.fromJson(Map<String, dynamic> json) =>
      _$VehicleYearFromJson(json);
}

/// Vehicle Year List Response
@freezed
abstract class VehicleYearListResponse with _$VehicleYearListResponse {
  const factory VehicleYearListResponse({
    required List<VehicleYear> years,
    required int total,
    @Default(false) bool hasMore,
  }) = _VehicleYearListResponse;

  factory VehicleYearListResponse.fromJson(Map<String, dynamic> json) =>
      _$VehicleYearListResponseFromJson(json);
}
