// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vehicle_year.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$VehicleYear {

 int get id; int get modelId; int get year; String? get modelName; String? get makeName;
/// Create a copy of VehicleYear
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleYearCopyWith<VehicleYear> get copyWith => _$VehicleYearCopyWithImpl<VehicleYear>(this as VehicleYear, _$identity);

  /// Serializes this VehicleYear to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleYear&&(identical(other.id, id) || other.id == id)&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.year, year) || other.year == year)&&(identical(other.modelName, modelName) || other.modelName == modelName)&&(identical(other.makeName, makeName) || other.makeName == makeName));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,modelId,year,modelName,makeName);

@override
String toString() {
  return 'VehicleYear(id: $id, modelId: $modelId, year: $year, modelName: $modelName, makeName: $makeName)';
}


}

/// @nodoc
abstract mixin class $VehicleYearCopyWith<$Res>  {
  factory $VehicleYearCopyWith(VehicleYear value, $Res Function(VehicleYear) _then) = _$VehicleYearCopyWithImpl;
@useResult
$Res call({
 int id, int modelId, int year, String? modelName, String? makeName
});




}
/// @nodoc
class _$VehicleYearCopyWithImpl<$Res>
    implements $VehicleYearCopyWith<$Res> {
  _$VehicleYearCopyWithImpl(this._self, this._then);

  final VehicleYear _self;
  final $Res Function(VehicleYear) _then;

/// Create a copy of VehicleYear
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? modelId = null,Object? year = null,Object? modelName = freezed,Object? makeName = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,modelId: null == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as int,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,modelName: freezed == modelName ? _self.modelName : modelName // ignore: cast_nullable_to_non_nullable
as String?,makeName: freezed == makeName ? _self.makeName : makeName // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleYear].
extension VehicleYearPatterns on VehicleYear {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleYear value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleYear() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleYear value)  $default,){
final _that = this;
switch (_that) {
case _VehicleYear():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleYear value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleYear() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  int modelId,  int year,  String? modelName,  String? makeName)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleYear() when $default != null:
return $default(_that.id,_that.modelId,_that.year,_that.modelName,_that.makeName);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  int modelId,  int year,  String? modelName,  String? makeName)  $default,) {final _that = this;
switch (_that) {
case _VehicleYear():
return $default(_that.id,_that.modelId,_that.year,_that.modelName,_that.makeName);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  int modelId,  int year,  String? modelName,  String? makeName)?  $default,) {final _that = this;
switch (_that) {
case _VehicleYear() when $default != null:
return $default(_that.id,_that.modelId,_that.year,_that.modelName,_that.makeName);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleYear implements VehicleYear {
  const _VehicleYear({required this.id, required this.modelId, required this.year, this.modelName, this.makeName});
  factory _VehicleYear.fromJson(Map<String, dynamic> json) => _$VehicleYearFromJson(json);

@override final  int id;
@override final  int modelId;
@override final  int year;
@override final  String? modelName;
@override final  String? makeName;

/// Create a copy of VehicleYear
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleYearCopyWith<_VehicleYear> get copyWith => __$VehicleYearCopyWithImpl<_VehicleYear>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleYearToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleYear&&(identical(other.id, id) || other.id == id)&&(identical(other.modelId, modelId) || other.modelId == modelId)&&(identical(other.year, year) || other.year == year)&&(identical(other.modelName, modelName) || other.modelName == modelName)&&(identical(other.makeName, makeName) || other.makeName == makeName));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,modelId,year,modelName,makeName);

@override
String toString() {
  return 'VehicleYear(id: $id, modelId: $modelId, year: $year, modelName: $modelName, makeName: $makeName)';
}


}

/// @nodoc
abstract mixin class _$VehicleYearCopyWith<$Res> implements $VehicleYearCopyWith<$Res> {
  factory _$VehicleYearCopyWith(_VehicleYear value, $Res Function(_VehicleYear) _then) = __$VehicleYearCopyWithImpl;
@override @useResult
$Res call({
 int id, int modelId, int year, String? modelName, String? makeName
});




}
/// @nodoc
class __$VehicleYearCopyWithImpl<$Res>
    implements _$VehicleYearCopyWith<$Res> {
  __$VehicleYearCopyWithImpl(this._self, this._then);

  final _VehicleYear _self;
  final $Res Function(_VehicleYear) _then;

/// Create a copy of VehicleYear
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? modelId = null,Object? year = null,Object? modelName = freezed,Object? makeName = freezed,}) {
  return _then(_VehicleYear(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,modelId: null == modelId ? _self.modelId : modelId // ignore: cast_nullable_to_non_nullable
as int,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,modelName: freezed == modelName ? _self.modelName : modelName // ignore: cast_nullable_to_non_nullable
as String?,makeName: freezed == makeName ? _self.makeName : makeName // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$VehicleYearListResponse {

 List<VehicleYear> get years; int get total; bool get hasMore;
/// Create a copy of VehicleYearListResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VehicleYearListResponseCopyWith<VehicleYearListResponse> get copyWith => _$VehicleYearListResponseCopyWithImpl<VehicleYearListResponse>(this as VehicleYearListResponse, _$identity);

  /// Serializes this VehicleYearListResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VehicleYearListResponse&&const DeepCollectionEquality().equals(other.years, years)&&(identical(other.total, total) || other.total == total)&&(identical(other.hasMore, hasMore) || other.hasMore == hasMore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(years),total,hasMore);

@override
String toString() {
  return 'VehicleYearListResponse(years: $years, total: $total, hasMore: $hasMore)';
}


}

/// @nodoc
abstract mixin class $VehicleYearListResponseCopyWith<$Res>  {
  factory $VehicleYearListResponseCopyWith(VehicleYearListResponse value, $Res Function(VehicleYearListResponse) _then) = _$VehicleYearListResponseCopyWithImpl;
@useResult
$Res call({
 List<VehicleYear> years, int total, bool hasMore
});




}
/// @nodoc
class _$VehicleYearListResponseCopyWithImpl<$Res>
    implements $VehicleYearListResponseCopyWith<$Res> {
  _$VehicleYearListResponseCopyWithImpl(this._self, this._then);

  final VehicleYearListResponse _self;
  final $Res Function(VehicleYearListResponse) _then;

/// Create a copy of VehicleYearListResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? years = null,Object? total = null,Object? hasMore = null,}) {
  return _then(_self.copyWith(
years: null == years ? _self.years : years // ignore: cast_nullable_to_non_nullable
as List<VehicleYear>,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int,hasMore: null == hasMore ? _self.hasMore : hasMore // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [VehicleYearListResponse].
extension VehicleYearListResponsePatterns on VehicleYearListResponse {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VehicleYearListResponse value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VehicleYearListResponse() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VehicleYearListResponse value)  $default,){
final _that = this;
switch (_that) {
case _VehicleYearListResponse():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VehicleYearListResponse value)?  $default,){
final _that = this;
switch (_that) {
case _VehicleYearListResponse() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<VehicleYear> years,  int total,  bool hasMore)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VehicleYearListResponse() when $default != null:
return $default(_that.years,_that.total,_that.hasMore);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<VehicleYear> years,  int total,  bool hasMore)  $default,) {final _that = this;
switch (_that) {
case _VehicleYearListResponse():
return $default(_that.years,_that.total,_that.hasMore);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<VehicleYear> years,  int total,  bool hasMore)?  $default,) {final _that = this;
switch (_that) {
case _VehicleYearListResponse() when $default != null:
return $default(_that.years,_that.total,_that.hasMore);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VehicleYearListResponse implements VehicleYearListResponse {
  const _VehicleYearListResponse({required final  List<VehicleYear> years, required this.total, this.hasMore = false}): _years = years;
  factory _VehicleYearListResponse.fromJson(Map<String, dynamic> json) => _$VehicleYearListResponseFromJson(json);

 final  List<VehicleYear> _years;
@override List<VehicleYear> get years {
  if (_years is EqualUnmodifiableListView) return _years;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_years);
}

@override final  int total;
@override@JsonKey() final  bool hasMore;

/// Create a copy of VehicleYearListResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VehicleYearListResponseCopyWith<_VehicleYearListResponse> get copyWith => __$VehicleYearListResponseCopyWithImpl<_VehicleYearListResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VehicleYearListResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VehicleYearListResponse&&const DeepCollectionEquality().equals(other._years, _years)&&(identical(other.total, total) || other.total == total)&&(identical(other.hasMore, hasMore) || other.hasMore == hasMore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_years),total,hasMore);

@override
String toString() {
  return 'VehicleYearListResponse(years: $years, total: $total, hasMore: $hasMore)';
}


}

/// @nodoc
abstract mixin class _$VehicleYearListResponseCopyWith<$Res> implements $VehicleYearListResponseCopyWith<$Res> {
  factory _$VehicleYearListResponseCopyWith(_VehicleYearListResponse value, $Res Function(_VehicleYearListResponse) _then) = __$VehicleYearListResponseCopyWithImpl;
@override @useResult
$Res call({
 List<VehicleYear> years, int total, bool hasMore
});




}
/// @nodoc
class __$VehicleYearListResponseCopyWithImpl<$Res>
    implements _$VehicleYearListResponseCopyWith<$Res> {
  __$VehicleYearListResponseCopyWithImpl(this._self, this._then);

  final _VehicleYearListResponse _self;
  final $Res Function(_VehicleYearListResponse) _then;

/// Create a copy of VehicleYearListResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? years = null,Object? total = null,Object? hasMore = null,}) {
  return _then(_VehicleYearListResponse(
years: null == years ? _self._years : years // ignore: cast_nullable_to_non_nullable
as List<VehicleYear>,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int,hasMore: null == hasMore ? _self.hasMore : hasMore // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
