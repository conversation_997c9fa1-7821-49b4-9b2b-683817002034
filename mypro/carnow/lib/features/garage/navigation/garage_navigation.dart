// ============================================================================
// GARAGE NAVIGATION - Forever Plan Architecture
// ============================================================================
// 
// Navigation utilities for the garage feature.
// Following Forever Plan principles:
// - Type-safe navigation
// - Clean API design
// - Performance optimized
// - Error handling
//
// ============================================================================

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Garage navigation utilities
class GarageNavigation {
  /// Route paths
  static const String garage = '/garage';
  static const String addVehicle = '/garage/add';
  static const String vehicleDetails = '/garage/vehicle';
  static const String editVehicle = '/edit';

  /// Navigate to garage screen
  static void goToGarage(BuildContext context) {
    context.go(garage);
  }

  /// Navigate to add vehicle screen
  static void goToAddVehicle(BuildContext context) {
    context.go(addVehicle);
  }

  /// Navigate to vehicle details screen
  static void goToVehicleDetails(BuildContext context, String vehicleId) {
    context.go('$vehicleDetails/$vehicleId');
  }

  /// Navigate to edit vehicle screen
  static void goToEditVehicle(BuildContext context, String vehicleId) {
    context.go('$vehicleDetails/$vehicleId$editVehicle');
  }

  /// Push garage screen
  static void pushGarage(BuildContext context) {
    context.push(garage);
  }

  /// Push add vehicle screen
  static void pushAddVehicle(BuildContext context) {
    context.push(addVehicle);
  }

  /// Push vehicle details screen
  static void pushVehicleDetails(BuildContext context, String vehicleId) {
    context.push('$vehicleDetails/$vehicleId');
  }

  /// Push edit vehicle screen
  static void pushEditVehicle(BuildContext context, String vehicleId) {
    context.push('$vehicleDetails/$vehicleId$editVehicle');
  }

  /// Check if current route is garage related
  static bool isGarageRoute(String route) {
    return route.startsWith(garage);
  }

  /// Get vehicle ID from route
  static String? getVehicleIdFromRoute(String route) {
    final regex = RegExp(r'/garage/vehicle/(\w+)');
    final match = regex.firstMatch(route);
    return match?.group(1);
  }

  /// Safe navigation with error handling
  static Future<bool> safeNavigateToGarage(BuildContext context) async {
    try {
      goToGarage(context);
      return true;
    } catch (e) {
      debugPrint('Failed to navigate to garage: $e');
      return false;
    }
  }

  /// Safe navigation to add vehicle with error handling
  static Future<bool> safeNavigateToAddVehicle(BuildContext context) async {
    try {
      goToAddVehicle(context);
      return true;
    } catch (e) {
      debugPrint('Failed to navigate to add vehicle: $e');
      return false;
    }
  }

  /// Safe navigation to vehicle details with error handling
  static Future<bool> safeNavigateToVehicleDetails(
    BuildContext context,
    String vehicleId,
  ) async {
    try {
      goToVehicleDetails(context, vehicleId);
      return true;
    } catch (e) {
      debugPrint('Failed to navigate to vehicle details: $e');
      return false;
    }
  }

  /// Safe navigation to edit vehicle with error handling
  static Future<bool> safeNavigateToEditVehicle(
    BuildContext context,
    String vehicleId,
  ) async {
    try {
      goToEditVehicle(context, vehicleId);
      return true;
    } catch (e) {
      debugPrint('Failed to navigate to edit vehicle: $e');
      return false;
    }
  }
}

/// Extension methods for easier navigation
extension GarageNavigationExtension on BuildContext {
  /// Navigate to garage
  void goToGarage() => GarageNavigation.goToGarage(this);

  /// Navigate to add vehicle
  void goToAddVehicle() => GarageNavigation.goToAddVehicle(this);

  /// Navigate to vehicle details
  void goToVehicleDetails(String vehicleId) =>
      GarageNavigation.goToVehicleDetails(this, vehicleId);

  /// Navigate to edit vehicle
  void goToEditVehicle(String vehicleId) =>
      GarageNavigation.goToEditVehicle(this, vehicleId);

  /// Push garage
  void pushGarage() => GarageNavigation.pushGarage(this);

  /// Push add vehicle
  void pushAddVehicle() => GarageNavigation.pushAddVehicle(this);

  /// Push vehicle details
  void pushVehicleDetails(String vehicleId) =>
      GarageNavigation.pushVehicleDetails(this, vehicleId);

  /// Push edit vehicle
  void pushEditVehicle(String vehicleId) =>
      GarageNavigation.pushEditVehicle(this, vehicleId);

  /// Safe navigate to garage
  Future<bool> safeGoToGarage() => GarageNavigation.safeNavigateToGarage(this);

  /// Safe navigate to add vehicle
  Future<bool> safeGoToAddVehicle() =>
      GarageNavigation.safeNavigateToAddVehicle(this);

  /// Safe navigate to vehicle details
  Future<bool> safeGoToVehicleDetails(String vehicleId) =>
      GarageNavigation.safeNavigateToVehicleDetails(this, vehicleId);

  /// Safe navigate to edit vehicle
  Future<bool> safeGoToEditVehicle(String vehicleId) =>
      GarageNavigation.safeNavigateToEditVehicle(this, vehicleId);
}
