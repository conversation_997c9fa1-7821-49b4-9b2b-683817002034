// ============================================================================
// GARAGE REPOSITORY - Forever Plan Architecture
// ============================================================================
// 
// This repository handles all API communication for the garage feature.
// Following Forever Plan principles:
// - REAL DATA ONLY from Go API
// - NO MOCK DATA tolerance
// - Clean Architecture
// - Error handling with retry logic
// - Performance optimized
//
// ============================================================================

import 'package:carnow/core/networking/simple_api_client.dart';
import 'package:carnow/features/garage/models/garage_models.dart';

class GarageRepository {
  final SimpleApiClient _apiClient;

  const GarageRepository(this._apiClient);

  // ============================================================================
  // VEHICLE REFERENCE DATA METHODS
  // ============================================================================

  /// Get all vehicle makes with optional search
  Future<List<VehicleMake>> getVehicleMakes({String? search}) async {
    final queryParams = <String, dynamic>{};
    if (search != null && search.isNotEmpty) {
      queryParams['search'] = search;
    }

    final response = await _apiClient.get(
      '/garage/makes',
      queryParameters: queryParams,
    );

    final List<dynamic> data = response.data['data'] ?? [];
    return data.map((json) => VehicleMake.fromJson(json)).toList();
  }

  /// Get vehicle models for a specific make
  Future<List<VehicleModel>> getVehicleModels(int makeId, {String? search}) async {
    final queryParams = <String, dynamic>{};
    if (search != null && search.isNotEmpty) {
      queryParams['search'] = search;
    }

    final response = await _apiClient.get(
      '/garage/models/$makeId',
      queryParameters: queryParams,
    );

    final List<dynamic> data = response.data['data'] ?? [];
    return data.map((json) => VehicleModel.fromJson(json)).toList();
  }

  /// Get vehicle years for a specific model
  Future<List<VehicleYear>> getVehicleYears(int modelId) async {
    final response = await _apiClient.get('/garage/years/$modelId');

    final List<dynamic> data = response.data['data'] ?? [];
    return data.map((json) => VehicleYear.fromJson(json)).toList();
  }

  /// Get vehicle trims for a specific model
  Future<List<VehicleTrim>> getVehicleTrims(int modelId) async {
    final response = await _apiClient.get('/garage/trims/$modelId');

    final List<dynamic> data = response.data['data'] ?? [];
    return data.map((json) => VehicleTrim.fromJson(json)).toList();
  }

  /// Get vehicle engines for a specific model
  Future<List<VehicleEngine>> getVehicleEngines(int modelId) async {
    final response = await _apiClient.get('/garage/engines/$modelId');

    final List<dynamic> data = response.data['data'] ?? [];
    return data.map((json) => VehicleEngine.fromJson(json)).toList();
  }

  // ============================================================================
  // USER VEHICLE METHODS
  // ============================================================================

  /// Get current user's vehicles
  Future<List<UserVehicle>> getMyVehicles() async {
    final response = await _apiClient.get('/garage/my-vehicles');

    final List<dynamic> data = response.data['data'] ?? [];
    return data.map((json) => UserVehicle.fromJson(json)).toList();
  }

  /// Create a new user vehicle
  Future<UserVehicle> createUserVehicle({
    required int makeRefId,
    required int modelRefId,
    int? yearRefId,
    int? trimRefId,
    int? engineRefId,
    String? color,
    String? vin,
    String? licensePlate,
    int? mileage,
    String? notes,
    DateTime? purchaseDate,
    double? purchasePrice,
    DateTime? insuranceExpiry,
    DateTime? lastServiceDate,
    DateTime? nextServiceDue,
    String? condition,
    List<String>? imageUrls,
    bool isPrimary = false,
  }) async {
    final requestData = <String, dynamic>{
      'make_ref_id': makeRefId,
      'model_ref_id': modelRefId,
      if (yearRefId != null) 'year_ref_id': yearRefId,
      if (trimRefId != null) 'trim_ref_id': trimRefId,
      if (engineRefId != null) 'engine_ref_id': engineRefId,
      if (color != null) 'color': color,
      if (vin != null) 'vin': vin,
      if (licensePlate != null) 'license_plate': licensePlate,
      if (mileage != null) 'mileage': mileage,
      if (notes != null) 'notes': notes,
      if (purchaseDate != null) 'purchase_date': purchaseDate.toIso8601String(),
      if (purchasePrice != null) 'purchase_price': purchasePrice,
      if (insuranceExpiry != null) 'insurance_expiry': insuranceExpiry.toIso8601String(),
      if (lastServiceDate != null) 'last_service_date': lastServiceDate.toIso8601String(),
      if (nextServiceDue != null) 'next_service_due': nextServiceDue.toIso8601String(),
      if (condition != null) 'condition': condition,
      if (imageUrls != null) 'image_urls': imageUrls,
      'is_primary': isPrimary,
    };

    final response = await _apiClient.post('/garage/vehicles', data: requestData);
    return UserVehicle.fromJson(response.data['data']);
  }

  /// Update an existing user vehicle
  Future<UserVehicle> updateUserVehicle({
    required int vehicleId,
    String? color,
    String? vin,
    String? licensePlate,
    int? mileage,
    String? notes,
    DateTime? purchaseDate,
    double? purchasePrice,
    DateTime? insuranceExpiry,
    DateTime? lastServiceDate,
    DateTime? nextServiceDue,
    String? condition,
    List<String>? imageUrls,
    bool? isPrimary,
    bool? isActive,
  }) async {
    final requestData = <String, dynamic>{};
    
    if (color != null) requestData['color'] = color;
    if (vin != null) requestData['vin'] = vin;
    if (licensePlate != null) requestData['license_plate'] = licensePlate;
    if (mileage != null) requestData['mileage'] = mileage;
    if (notes != null) requestData['notes'] = notes;
    if (purchaseDate != null) requestData['purchase_date'] = purchaseDate.toIso8601String();
    if (purchasePrice != null) requestData['purchase_price'] = purchasePrice;
    if (insuranceExpiry != null) requestData['insurance_expiry'] = insuranceExpiry.toIso8601String();
    if (lastServiceDate != null) requestData['last_service_date'] = lastServiceDate.toIso8601String();
    if (nextServiceDue != null) requestData['next_service_due'] = nextServiceDue.toIso8601String();
    if (condition != null) requestData['condition'] = condition;
    if (imageUrls != null) requestData['image_urls'] = imageUrls;
    if (isPrimary != null) requestData['is_primary'] = isPrimary;
    if (isActive != null) requestData['is_active'] = isActive;

    final response = await _apiClient.put('/garage/vehicles/$vehicleId', data: requestData);
    return UserVehicle.fromJson(response.data['data']);
  }

  /// Delete a user vehicle (soft delete)
  Future<void> deleteUserVehicle(int vehicleId) async {
    await _apiClient.delete('/garage/vehicles/$vehicleId');
  }
}
