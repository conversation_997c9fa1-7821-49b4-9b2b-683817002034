// ============================================================================
// GARAGE ROUTES - Forever Plan Architecture
// ============================================================================
// 
// Route definitions for the garage feature.
// Following Forever Plan principles:
// - Clean routing structure
// - Type-safe navigation
// - Performance optimized
// - Accessibility compliant
//
// ============================================================================

import 'package:go_router/go_router.dart';
import 'package:carnow/features/garage/screens/garage_screen.dart';
import 'package:carnow/features/garage/screens/add_vehicle_screen.dart';
import 'package:carnow/features/garage/screens/vehicle_details_screen.dart';
import 'package:carnow/features/garage/screens/edit_vehicle_screen.dart';

/// Garage feature routes
final List<RouteBase> garageRoutes = [
  // Main garage screen
  GoRoute(
    path: '/garage',
    name: 'garage',
    builder: (context, state) => const GarageScreen(),
    routes: [
      // Add vehicle screen
      GoRoute(
        path: '/add',
        name: 'garage-add',
        builder: (context, state) => const AddVehicleScreen(),
      ),
      
      // Vehicle details screen
      GoRoute(
        path: '/vehicle/:vehicleId',
        name: 'garage-vehicle-details',
        builder: (context, state) {
          final vehicleId = state.pathParameters['vehicleId']!;
          return VehicleDetailsScreen(vehicleId: vehicleId);
        },
        routes: [
          // Edit vehicle screen
          GoRoute(
            path: '/edit',
            name: 'garage-vehicle-edit',
            builder: (context, state) {
              final vehicleId = state.pathParameters['vehicleId']!;
              return EditVehicleScreen(vehicleId: vehicleId);
            },
          ),
        ],
      ),
    ],
  ),
];

/// Garage navigation extensions
extension GarageNavigation on GoRouter {
  /// Navigate to garage screen
  void goToGarage() => go('/garage');
  
  /// Navigate to add vehicle screen
  void goToAddVehicle() => go('/garage/add');
  
  /// Navigate to vehicle details screen
  void goToVehicleDetails(String vehicleId) => go('/garage/vehicle/$vehicleId');
  
  /// Navigate to edit vehicle screen
  void goToEditVehicle(String vehicleId) => go('/garage/vehicle/$vehicleId/edit');
  
  /// Push garage screen
  void pushGarage() => push('/garage');
  
  /// Push add vehicle screen
  void pushAddVehicle() => push('/garage/add');
  
  /// Push vehicle details screen
  void pushVehicleDetails(String vehicleId) => push('/garage/vehicle/$vehicleId');
  
  /// Push edit vehicle screen
  void pushEditVehicle(String vehicleId) => push('/garage/vehicle/$vehicleId/edit');
}
