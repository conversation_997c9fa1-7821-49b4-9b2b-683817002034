// ============================================================================
// ADD VEHICLE SCREEN - Forever Plan Architecture
// ============================================================================
// 
// Screen for adding a new vehicle to the garage with hierarchical selection.
// Following Forever Plan principles:
// - REAL DATA ONLY from providers
// - NO MOCK DATA tolerance
// - Material 3 Design System
// - Accessibility compliant
// - Performance optimized
//
// ============================================================================

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:carnow/core/theme/app_colors.dart';

import 'package:carnow/features/garage/providers/garage_providers.dart';
import 'package:carnow/features/garage/widgets/vehicle_selection_stepper.dart';
import 'package:carnow/features/garage/widgets/vehicle_details_form.dart';

class AddVehicleScreen extends ConsumerStatefulWidget {
  const AddVehicleScreen({super.key});

  @override
  ConsumerState<AddVehicleScreen> createState() => _AddVehicleScreenState();
}

class _AddVehicleScreenState extends ConsumerState<AddVehicleScreen> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  
  // Vehicle details form data
  final _formKey = GlobalKey<FormState>();
  final _colorController = TextEditingController();
  final _vinController = TextEditingController();
  final _licensePlateController = TextEditingController();
  final _mileageController = TextEditingController();
  final _notesController = TextEditingController();
  bool _isPrimary = false;

  @override
  void dispose() {
    _pageController.dispose();
    _colorController.dispose();
    _vinController.dispose();
    _licensePlateController.dispose();
    _mileageController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final garageActions = ref.watch(garageActionsProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('إضافة سيارة جديدة'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        scrolledUnderElevation: 1,
        leading: IconButton(
          onPressed: () {
            if (_currentStep > 0) {
              _previousStep();
            } else {
              context.pop();
            }
          },
          icon: const Icon(Icons.arrow_back_rounded),
        ),
        actions: [
          if (_currentStep == 1 && ref.read(vehicleSelectionStateProvider.notifier).canCreateVehicle)
            TextButton(
              onPressed: ref.watch(garageActionsProvider).isLoading ? null : _skipToCreate,
              child: const Text('تخطي'),
            ),
        ],
      ),
      body: Column(
        children: [
          // Progress indicator
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Expanded(
                  child: LinearProgressIndicator(
                    value: (_currentStep + 1) / 2,
                    backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryBrand),
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  '${_currentStep + 1} من 2',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                // Step 1: Vehicle Selection
                VehicleSelectionStepper(
                  onSelectionComplete: _nextStep,
                ),

                // Step 2: Vehicle Details
                VehicleDetailsForm(
                  formKey: _formKey,
                  colorController: _colorController,
                  vinController: _vinController,
                  licensePlateController: _licensePlateController,
                  mileageController: _mileageController,
                  notesController: _notesController,
                  isPrimary: _isPrimary,
                  onIsPrimaryChanged: (value) => setState(() => _isPrimary = value),
                ),
              ],
            ),
          ),

          // Bottom actions
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                ),
              ),
            ),
            child: Row(
              children: [
                if (_currentStep > 0)
                  Expanded(
                    child: OutlinedButton(
                      onPressed: garageActions.isLoading ? null : _previousStep,
                      child: const Text('السابق'),
                    ),
                  ),
                if (_currentStep > 0) const SizedBox(width: 16),
                Expanded(
                  child: FilledButton(
                    onPressed: _getNextButtonAction(),
                    style: FilledButton.styleFrom(
                      backgroundColor: AppColors.primaryBrand,
                    ),
                    child: garageActions.isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(_getNextButtonText()),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  VoidCallback? _getNextButtonAction() {
    final garageActions = ref.read(garageActionsProvider);
    if (garageActions.isLoading) return null;

    switch (_currentStep) {
      case 0:
        final canCreate = ref.read(vehicleSelectionStateProvider.notifier).canCreateVehicle;
        return canCreate ? _nextStep : null;
      case 1:
        return _createVehicle;
      default:
        return null;
    }
  }

  String _getNextButtonText() {
    switch (_currentStep) {
      case 0:
        return 'التالي';
      case 1:
        return 'إضافة السيارة';
      default:
        return 'التالي';
    }
  }

  void _nextStep() {
    if (_currentStep < 1) {
      setState(() => _currentStep++);
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() => _currentStep--);
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _skipToCreate() {
    setState(() => _currentStep = 1);
    _pageController.animateToPage(
      1,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  Future<void> _createVehicle() async {
    if (!_formKey.currentState!.validate()) return;

    final vehicleSelection = ref.read(vehicleSelectionStateProvider);
    
    if (vehicleSelection.selectedMake == null || vehicleSelection.selectedModel == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار الماركة والموديل على الأقل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      await ref.read(garageActionsProvider.notifier).createVehicle(
        makeRefId: vehicleSelection.selectedMake!.id,
        modelRefId: vehicleSelection.selectedModel!.id,
        yearRefId: vehicleSelection.selectedYear?.id,
        trimRefId: vehicleSelection.selectedTrim?.id,
        engineRefId: vehicleSelection.selectedEngine?.id,
        color: _colorController.text.isNotEmpty ? _colorController.text : null,
        vin: _vinController.text.isNotEmpty ? _vinController.text : null,
        licensePlate: _licensePlateController.text.isNotEmpty ? _licensePlateController.text : null,
        mileage: _mileageController.text.isNotEmpty ? int.tryParse(_mileageController.text) : null,
        isPrimary: _isPrimary,
      );

      // Reset selection state
      ref.read(vehicleSelectionStateProvider.notifier).reset();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('تم إضافة السيارة بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );

        // Navigate back to garage
        context.pop();
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة السيارة: $error'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}
