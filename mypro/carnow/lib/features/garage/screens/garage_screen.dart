// ============================================================================
// GARAGE SCREEN - Forever Plan Architecture
// ============================================================================
// 
// Main garage screen showing user's vehicles with Material 3 design.
// Following Forever Plan principles:
// - REAL DATA ONLY from providers
// - NO MOCK DATA tolerance
// - Material 3 Design System
// - Accessibility compliant
// - Performance optimized
//
// ============================================================================

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:carnow/core/theme/app_colors.dart';
import 'package:carnow/core/widgets/app_error_widget.dart';
import 'package:carnow/features/garage/providers/garage_providers.dart';
import 'package:carnow/features/garage/widgets/vehicle_card.dart';
import 'package:carnow/features/garage/widgets/empty_garage_widget.dart';

class GarageScreen extends ConsumerWidget {
  const GarageScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final vehiclesAsync = ref.watch(myVehiclesProvider);
    final garageActions = ref.watch(garageActionsProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'مرآبي',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        scrolledUnderElevation: 1,
        actions: [
          IconButton(
            onPressed: () => context.push('/garage/add'),
            icon: Icon(
              Icons.add_rounded,
              color: AppColors.primaryBrand,
            ),
            tooltip: 'إضافة سيارة جديدة',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(myVehiclesProvider);
        },
        child: vehiclesAsync.when(
          data: (vehicles) {
            if (vehicles.isEmpty) {
              return const EmptyGarageWidget();
            }

            return CustomScrollView(
              slivers: [
                // Primary vehicle section
                if (vehicles.any((v) => v.isPrimary)) ...[
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'السيارة الأساسية',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: AppColors.primaryBrand,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 12),
                          VehicleCard(
                            vehicle: vehicles.firstWhere((v) => v.isPrimary),
                            isPrimary: true,
                            onTap: () => _onVehicleTap(context, vehicles.firstWhere((v) => v.isPrimary)),
                            onEdit: () => _onEditVehicle(context, vehicles.firstWhere((v) => v.isPrimary)),
                            onDelete: () => _onDeleteVehicle(context, ref, vehicles.firstWhere((v) => v.isPrimary)),
                            onSetPrimary: null, // Already primary
                          ),
                        ],
                      ),
                    ),
                  ),
                ],

                // Other vehicles section
                if (vehicles.where((v) => !v.isPrimary).isNotEmpty) ...[
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Text(
                        vehicles.any((v) => v.isPrimary) ? 'السيارات الأخرى' : 'سياراتي',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  const SliverToBoxAdapter(child: SizedBox(height: 12)),
                ],

                // Vehicles list
                SliverPadding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  sliver: SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final otherVehicles = vehicles.where((v) => !v.isPrimary).toList();
                        if (index >= otherVehicles.length) return null;
                        
                        final vehicle = otherVehicles[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 12.0),
                          child: VehicleCard(
                            vehicle: vehicle,
                            isPrimary: false,
                            onTap: () => _onVehicleTap(context, vehicle),
                            onEdit: () => _onEditVehicle(context, vehicle),
                            onDelete: () => _onDeleteVehicle(context, ref, vehicle),
                            onSetPrimary: () => _onSetPrimary(context, ref, vehicle),
                          ),
                        );
                      },
                      childCount: vehicles.where((v) => !v.isPrimary).length,
                    ),
                  ),
                ),

                // Bottom padding
                const SliverToBoxAdapter(child: SizedBox(height: 100)),
              ],
            );
          },
          loading: () => const Center(
            child: CircularProgressIndicator(),
          ),
          error: (error, stackTrace) => Center(
            child: AppErrorWidget(
              message: 'خطأ في تحميل السيارات',
              details: error.toString(),
              onRetry: () => ref.invalidate(myVehiclesProvider),
            ),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: garageActions.isLoading 
          ? null 
          : () => context.push('/garage/add'),
        backgroundColor: AppColors.primaryBrand,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add_rounded),
        label: const Text('إضافة سيارة'),
        tooltip: 'إضافة سيارة جديدة',
      ),
    );
  }

  void _onVehicleTap(BuildContext context, vehicle) {
    context.push('/garage/vehicle/${vehicle.id}');
  }

  void _onEditVehicle(BuildContext context, vehicle) {
    context.push('/garage/vehicle/${vehicle.id}/edit');
  }

  void _onDeleteVehicle(BuildContext context, WidgetRef ref, vehicle) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف السيارة'),
        content: Text('هل أنت متأكد من حذف ${vehicle.make} ${vehicle.model}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(garageActionsProvider.notifier).deleteVehicle(vehicle.id);
            },
            style: FilledButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _onSetPrimary(BuildContext context, WidgetRef ref, vehicle) {
    ref.read(garageActionsProvider.notifier).setPrimaryVehicle(vehicle.id);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تعيين ${vehicle.make} ${vehicle.model} كسيارة أساسية'),
        backgroundColor: AppColors.success,
      ),
    );
  }
}
