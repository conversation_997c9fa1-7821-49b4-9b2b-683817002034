// ============================================================================
// VEHICLE DETAILS SCREEN - Forever Plan Architecture
// ============================================================================
// 
// Screen for viewing detailed information about a specific vehicle.
// Following Forever Plan principles:
// - REAL DATA ONLY from providers
// - NO MOCK DATA tolerance
// - Material 3 Design System
// - Accessibility compliant
// - Performance optimized
//
// ============================================================================

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:carnow/core/theme/app_colors.dart';
import 'package:carnow/features/garage/providers/garage_providers.dart';

class VehicleDetailsScreen extends ConsumerWidget {
  final String vehicleId;

  const VehicleDetailsScreen({
    super.key,
    required this.vehicleId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final vehiclesAsync = ref.watch(myVehiclesProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: vehiclesAsync.when(
        data: (vehicles) {
          final vehicle = vehicles.firstWhere(
            (v) => v.id == vehicleId,
            orElse: () => throw StateError('Vehicle not found'),
          );

          return CustomScrollView(
            slivers: [
              // App bar
              SliverAppBar(
                expandedHeight: 200,
                pinned: true,
                backgroundColor: Theme.of(context).colorScheme.surface,
                flexibleSpace: FlexibleSpaceBar(
                  title: Text(
                    '${vehicle.year} ${vehicle.make} ${vehicle.model}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  background: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          AppColors.primaryBrand.withOpacity(0.8),
                          AppColors.primaryBrand.withOpacity(0.4),
                        ],
                      ),
                    ),
                    child: Center(
                      child: Icon(
                        Icons.directions_car_rounded,
                        size: 80,
                        color: Colors.white.withOpacity(0.8),
                      ),
                    ),
                  ),
                ),
                actions: [
                  IconButton(
                    onPressed: () => context.push('/garage/vehicle/$vehicleId/edit'),
                    icon: const Icon(Icons.edit_rounded),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) async {
                      switch (value) {
                        case 'set_primary':
                          await _setPrimaryVehicle(ref, vehicleId);
                          break;
                        case 'delete':
                          await _deleteVehicle(context, ref, vehicleId);
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      if (!vehicle.isPrimary)
                        const PopupMenuItem(
                          value: 'set_primary',
                          child: Row(
                            children: [
                              Icon(Icons.star_rounded),
                              SizedBox(width: 8),
                              Text('تعيين كأساسية'),
                            ],
                          ),
                        ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete_rounded, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              // Content
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Primary badge
                      if (vehicle.isPrimary) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: AppColors.primaryBrand,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.star_rounded,
                                color: Colors.white,
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'السيارة الأساسية',
                                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 24),
                      ],

                      // Vehicle specifications
                      _buildSection(
                        context,
                        title: 'مواصفات السيارة',
                        icon: Icons.info_rounded,
                        children: [
                          _buildDetailRow(context, 'الماركة', vehicle.make),
                          _buildDetailRow(context, 'الموديل', vehicle.model),
                          _buildDetailRow(context, 'السنة', vehicle.year.toString()),
                          if (vehicle.trim?.isNotEmpty == true)
                            _buildDetailRow(context, 'الفئة', vehicle.trim!),
                          if (vehicle.engine?.isNotEmpty == true)
                            _buildDetailRow(context, 'المحرك', vehicle.engine!),
                        ],
                      ),

                      const SizedBox(height: 24),

                      // Vehicle details
                      _buildSection(
                        context,
                        title: 'تفاصيل السيارة',
                        icon: Icons.description_rounded,
                        children: [
                          if (vehicle.color?.isNotEmpty == true)
                            _buildDetailRow(context, 'اللون', vehicle.color!),
                          if (vehicle.mileage > 0)
                            _buildDetailRow(context, 'المسافة المقطوعة', '${vehicle.mileage} كم'),
                          if (vehicle.licensePlate?.isNotEmpty == true)
                            _buildDetailRow(context, 'رقم اللوحة', vehicle.licensePlate!),
                          if (vehicle.vin?.isNotEmpty == true)
                            _buildDetailRow(context, 'رقم الهيكل', vehicle.vin!),
                        ],
                      ),

                      const SizedBox(height: 24),

                      // Service information
                      if (vehicle.nextServiceDue != null || vehicle.insuranceExpiry != null)
                        _buildSection(
                          context,
                          title: 'معلومات الصيانة',
                          icon: Icons.build_rounded,
                          children: [
                            if (vehicle.nextServiceDue != null)
                              _buildDetailRow(
                                context,
                                'موعد الصيانة القادم',
                                _formatDate(vehicle.nextServiceDue!),
                              ),
                            if (vehicle.insuranceExpiry != null)
                              _buildDetailRow(
                                context,
                                'انتهاء التأمين',
                                _formatDate(vehicle.insuranceExpiry!),
                              ),
                          ],
                        ),

                      const SizedBox(height: 24),

                      // Notes section removed - not available in current UserVehicle model

                      const SizedBox(height: 32),

                      // Action buttons
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: () => context.push('/garage/vehicle/$vehicleId/edit'),
                              icon: const Icon(Icons.edit_rounded),
                              label: const Text('تعديل'),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: FilledButton.icon(
                              onPressed: () {
                                // Navigate to parts search for this vehicle
                                // TODO: Implement parts search navigation
                              },
                              style: FilledButton.styleFrom(
                                backgroundColor: AppColors.primaryBrand,
                              ),
                              icon: const Icon(Icons.search_rounded),
                              label: const Text('البحث عن قطع غيار'),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
        loading: () => const Scaffold(
          body: Center(child: CircularProgressIndicator()),
        ),
        error: (error, stackTrace) => Scaffold(
          appBar: AppBar(title: const Text('خطأ')),
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('خطأ في تحميل بيانات السيارة'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => ref.invalidate(myVehiclesProvider),
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: AppColors.primaryBrand,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildDetailRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _setPrimaryVehicle(WidgetRef ref, String vehicleId) async {
    try {
      await ref.read(garageActionsProvider.notifier).setPrimaryVehicle(int.parse(vehicleId));
    } catch (error) {
      // Handle error
    }
  }

  Future<void> _deleteVehicle(BuildContext context, WidgetRef ref, String vehicleId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف السيارة'),
        content: const Text('هل أنت متأكد من حذف هذه السيارة؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: FilledButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref.read(garageActionsProvider.notifier).deleteVehicle(int.parse(vehicleId));
        if (context.mounted) {
          context.pop(); // Go back to garage screen
        }
      } catch (error) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف السيارة: $error'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    }
  }
}
