// ============================================================================
// GARAGE SERVICE - Forever Plan Architecture
// ============================================================================
// 
// This service provides business logic for the garage feature.
// Following Forever Plan principles:
// - REAL DATA ONLY from repository
// - NO MOCK DATA tolerance
// - Business logic layer
// - Error handling and validation
// - Performance optimized
//
// ============================================================================

import 'package:carnow/features/garage/models/garage_models.dart';
import 'package:carnow/features/garage/repositories/garage_repository.dart';

class GarageService {
  final GarageRepository _repository;

  const GarageService(this._repository);

  // ============================================================================
  // VEHICLE REFERENCE DATA METHODS
  // ============================================================================

  /// Get all vehicle makes with optional search and caching
  Future<List<VehicleMake>> getVehicleMakes({String? search}) async {
    final makes = await _repository.getVehicleMakes(search: search);
    
    // Sort by name for better UX
    makes.sort((a, b) => a.name.compareTo(b.name));
    
    return makes;
  }

  /// Get vehicle models for a specific make with validation
  Future<List<VehicleModel>> getVehicleModels(int makeId, {String? search}) async {
    if (makeId <= 0) {
      throw ArgumentError('Make ID must be positive');
    }

    final models = await _repository.getVehicleModels(makeId, search: search);
    
    // Sort by name for better UX
    models.sort((a, b) => a.name.compareTo(b.name));
    
    return models;
  }

  /// Get vehicle years for a specific model with validation
  Future<List<VehicleYear>> getVehicleYears(int modelId) async {
    if (modelId <= 0) {
      throw ArgumentError('Model ID must be positive');
    }

    final years = await _repository.getVehicleYears(modelId);
    
    // Sort by year descending (newest first)
    years.sort((a, b) => b.year.compareTo(a.year));
    
    return years;
  }

  /// Get vehicle trims for a specific model with validation
  Future<List<VehicleTrim>> getVehicleTrims(int modelId) async {
    if (modelId <= 0) {
      throw ArgumentError('Model ID must be positive');
    }

    final trims = await _repository.getVehicleTrims(modelId);
    
    // Sort by trim level and name
    trims.sort((a, b) {
      final levelComparison = (a.trimLevel ?? '').compareTo(b.trimLevel ?? '');
      if (levelComparison != 0) return levelComparison;
      return a.name.compareTo(b.name);
    });
    
    return trims;
  }

  /// Get vehicle engines for a specific model with validation
  Future<List<VehicleEngine>> getVehicleEngines(int modelId) async {
    if (modelId <= 0) {
      throw ArgumentError('Model ID must be positive');
    }

    final engines = await _repository.getVehicleEngines(modelId);
    
    // Sort by displacement and power
    engines.sort((a, b) {
      final displacementComparison = a.displacementCc.compareTo(b.displacementCc);
      if (displacementComparison != 0) return displacementComparison;
      return a.powerHp.compareTo(b.powerHp);
    });
    
    return engines;
  }

  // ============================================================================
  // USER VEHICLE METHODS
  // ============================================================================

  /// Get current user's vehicles with sorting
  Future<List<UserVehicle>> getMyVehicles() async {
    final vehicles = await _repository.getMyVehicles();
    
    // Sort by primary first, then by creation date
    vehicles.sort((a, b) {
      if (a.isPrimary && !b.isPrimary) return -1;
      if (!a.isPrimary && b.isPrimary) return 1;
      return b.createdAt.compareTo(a.createdAt);
    });
    
    return vehicles;
  }

  /// Create a new user vehicle with validation
  Future<UserVehicle> createUserVehicle({
    required int makeRefId,
    required int modelRefId,
    int? yearRefId,
    int? trimRefId,
    int? engineRefId,
    String? color,
    String? vin,
    String? licensePlate,
    int? mileage,
    String? notes,
    DateTime? purchaseDate,
    double? purchasePrice,
    DateTime? insuranceExpiry,
    DateTime? lastServiceDate,
    DateTime? nextServiceDue,
    String? condition,
    List<String>? imageUrls,
    bool isPrimary = false,
  }) async {
    // Validation
    if (makeRefId <= 0) {
      throw ArgumentError('Make reference ID must be positive');
    }
    if (modelRefId <= 0) {
      throw ArgumentError('Model reference ID must be positive');
    }
    if (mileage != null && mileage < 0) {
      throw ArgumentError('Mileage cannot be negative');
    }
    if (purchasePrice != null && purchasePrice < 0) {
      throw ArgumentError('Purchase price cannot be negative');
    }
    if (vin != null && vin.length > 17) {
      throw ArgumentError('VIN cannot be longer than 17 characters');
    }

    return await _repository.createUserVehicle(
      makeRefId: makeRefId,
      modelRefId: modelRefId,
      yearRefId: yearRefId,
      trimRefId: trimRefId,
      engineRefId: engineRefId,
      color: color,
      vin: vin,
      licensePlate: licensePlate,
      mileage: mileage,
      notes: notes,
      purchaseDate: purchaseDate,
      purchasePrice: purchasePrice,
      insuranceExpiry: insuranceExpiry,
      lastServiceDate: lastServiceDate,
      nextServiceDue: nextServiceDue,
      condition: condition,
      imageUrls: imageUrls,
      isPrimary: isPrimary,
    );
  }

  /// Update an existing user vehicle with validation
  Future<UserVehicle> updateUserVehicle({
    required int vehicleId,
    String? color,
    String? vin,
    String? licensePlate,
    int? mileage,
    String? notes,
    DateTime? purchaseDate,
    double? purchasePrice,
    DateTime? insuranceExpiry,
    DateTime? lastServiceDate,
    DateTime? nextServiceDue,
    String? condition,
    List<String>? imageUrls,
    bool? isPrimary,
    bool? isActive,
  }) async {
    // Validation
    if (vehicleId <= 0) {
      throw ArgumentError('Vehicle ID must be positive');
    }
    if (mileage != null && mileage < 0) {
      throw ArgumentError('Mileage cannot be negative');
    }
    if (purchasePrice != null && purchasePrice < 0) {
      throw ArgumentError('Purchase price cannot be negative');
    }
    if (vin != null && vin.length > 17) {
      throw ArgumentError('VIN cannot be longer than 17 characters');
    }

    return await _repository.updateUserVehicle(
      vehicleId: vehicleId,
      color: color,
      vin: vin,
      licensePlate: licensePlate,
      mileage: mileage,
      notes: notes,
      purchaseDate: purchaseDate,
      purchasePrice: purchasePrice,
      insuranceExpiry: insuranceExpiry,
      lastServiceDate: lastServiceDate,
      nextServiceDue: nextServiceDue,
      condition: condition,
      imageUrls: imageUrls,
      isPrimary: isPrimary,
      isActive: isActive,
    );
  }

  /// Delete a user vehicle with validation
  Future<void> deleteUserVehicle(int vehicleId) async {
    if (vehicleId <= 0) {
      throw ArgumentError('Vehicle ID must be positive');
    }

    await _repository.deleteUserVehicle(vehicleId);
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /// Generate a display name for a vehicle
  String generateVehicleDisplayName({
    required String make,
    required String model,
    required int year,
    String? trim,
  }) {
    final parts = <String>[year.toString(), make, model];
    if (trim != null && trim.isNotEmpty) {
      parts.add(trim);
    }
    return parts.join(' ');
  }

  /// Check if a vehicle needs service based on dates and mileage
  bool needsService(UserVehicle vehicle) {
    final now = DateTime.now();
    
    // Check if next service is due
    if (vehicle.nextServiceDue != null && vehicle.nextServiceDue!.isBefore(now)) {
      return true;
    }
    
    // Check if insurance is expired
    if (vehicle.insuranceExpiry != null && vehicle.insuranceExpiry!.isBefore(now)) {
      return true;
    }
    
    return false;
  }

  /// Get service status for a vehicle
  String getServiceStatus(UserVehicle vehicle) {
    final now = DateTime.now();
    
    if (vehicle.insuranceExpiry != null && vehicle.insuranceExpiry!.isBefore(now)) {
      return 'insurance_expired';
    }
    
    if (vehicle.nextServiceDue != null && vehicle.nextServiceDue!.isBefore(now)) {
      return 'service_due';
    }
    
    if (vehicle.nextServiceDue != null) {
      final daysUntilService = vehicle.nextServiceDue!.difference(now).inDays;
      if (daysUntilService <= 30) {
        return 'service_soon';
      }
    }
    
    return 'up_to_date';
  }
}
