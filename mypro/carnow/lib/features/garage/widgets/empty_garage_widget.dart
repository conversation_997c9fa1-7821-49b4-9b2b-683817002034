// ============================================================================
// EMPTY GARAGE WIDGET - Forever Plan Architecture
// ============================================================================
// 
// Widget displayed when user has no vehicles in their garage.
// Following Forever Plan principles:
// - Material 3 Design System
// - Accessibility compliant
// - Performance optimized
// - User-friendly empty state
//
// ============================================================================

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:carnow/core/theme/app_colors.dart';

class EmptyGarageWidget extends StatelessWidget {
  const EmptyGarageWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Illustration
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                color: AppColors.primaryBrand.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.garage_rounded,
                size: 100,
                color: AppColors.primaryBrand.withOpacity(0.6),
              ),
            ),

            const SizedBox(height: 32),

            // Title
            Text(
              'مرآبك فارغ',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // Description
            Text(
              'ابدأ بإضافة سياراتك لتتمكن من العثور على قطع الغيار المناسبة والحصول على توصيات مخصصة',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 32),

            // Add vehicle button
            FilledButton.icon(
              onPressed: () => context.push('/garage/add'),
              style: FilledButton.styleFrom(
                backgroundColor: AppColors.primaryBrand,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              ),
              icon: const Icon(Icons.add_rounded),
              label: const Text(
                'إضافة سيارتي الأولى',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Benefits list
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'فوائد إضافة سيارتك:',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildBenefitItem(
                    context,
                    icon: Icons.search_rounded,
                    title: 'قطع غيار متوافقة',
                    description: 'العثور على قطع الغيار المناسبة لسيارتك بدقة',
                  ),
                  const SizedBox(height: 12),
                  _buildBenefitItem(
                    context,
                    icon: Icons.recommend_rounded,
                    title: 'توصيات مخصصة',
                    description: 'الحصول على توصيات منتجات مناسبة لسيارتك',
                  ),
                  const SizedBox(height: 12),
                  _buildBenefitItem(
                    context,
                    icon: Icons.build_rounded,
                    title: 'تذكير الصيانة',
                    description: 'تتبع مواعيد الصيانة والتأمين',
                  ),
                  const SizedBox(height: 12),
                  _buildBenefitItem(
                    context,
                    icon: Icons.history_rounded,
                    title: 'تاريخ المشتريات',
                    description: 'تتبع مشترياتك وتاريخ الصيانة',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBenefitItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: AppColors.primaryBrand.withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            icon,
            size: 16,
            color: AppColors.primaryBrand,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
