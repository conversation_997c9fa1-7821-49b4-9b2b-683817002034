// ============================================================================
// VEHICLE CARD WIDGET - Forever Plan Architecture
// ============================================================================
// 
// Card widget for displaying vehicle information with Material 3 design.
// Following Forever Plan principles:
// - REAL DATA ONLY display
// - NO MOCK DATA tolerance
// - Material 3 Design System
// - Accessibility compliant
// - Performance optimized
//
// ============================================================================

import 'package:flutter/material.dart';
import 'package:carnow/core/theme/app_colors.dart';
import 'package:carnow/features/garage/models/garage_models.dart';

class VehicleCard extends StatelessWidget {
  final UserVehicle vehicle;
  final bool isPrimary;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onSetPrimary;

  const VehicleCard({
    super.key,
    required this.vehicle,
    required this.isPrimary,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onSetPrimary,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isPrimary ? 4 : 2,
      shadowColor: isPrimary ? AppColors.primaryBrand.withOpacity(0.3) : null,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: isPrimary
                ? Border.all(
                    color: AppColors.primaryBrand.withOpacity(0.3),
                    width: 2,
                  )
                : null,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with primary badge and actions
              Row(
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        // Vehicle icon
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: AppColors.primaryBrand.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.directions_car_rounded,
                            color: AppColors.primaryBrand,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 12),
                        
                        // Vehicle name and primary badge
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '${vehicle.year} ${vehicle.make} ${vehicle.model}',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).colorScheme.onSurface,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              if (isPrimary) ...[
                                const SizedBox(height: 4),
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: AppColors.primaryBrand,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    'السيارة الأساسية',
                                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Actions menu
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          onEdit?.call();
                          break;
                        case 'set_primary':
                          onSetPrimary?.call();
                          break;
                        case 'delete':
                          onDelete?.call();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit_rounded),
                            SizedBox(width: 8),
                            Text('تعديل'),
                          ],
                        ),
                      ),
                      if (!isPrimary && onSetPrimary != null)
                        const PopupMenuItem(
                          value: 'set_primary',
                          child: Row(
                            children: [
                              Icon(Icons.star_rounded),
                              SizedBox(width: 8),
                              Text('تعيين كأساسية'),
                            ],
                          ),
                        ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete_rounded, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                    child: Icon(
                      Icons.more_vert_rounded,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Vehicle details
              Row(
                children: [
                  Expanded(
                    child: _buildDetailItem(
                      context,
                      icon: Icons.palette_rounded,
                      label: 'اللون',
                      value: (vehicle.color?.isNotEmpty == true) ? vehicle.color! : 'غير محدد',
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildDetailItem(
                      context,
                      icon: Icons.speed_rounded,
                      label: 'المسافة المقطوعة',
                      value: vehicle.mileage > 0 ? '${vehicle.mileage.toString()} كم' : 'غير محدد',
                    ),
                  ),
                ],
              ),

              if ((vehicle.licensePlate?.isNotEmpty == true) || (vehicle.vin?.isNotEmpty == true)) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    if (vehicle.licensePlate?.isNotEmpty == true)
                      Expanded(
                        child: _buildDetailItem(
                          context,
                          icon: Icons.confirmation_number_rounded,
                          label: 'رقم اللوحة',
                          value: vehicle.licensePlate!,
                        ),
                      ),
                    if ((vehicle.licensePlate?.isNotEmpty == true) && (vehicle.vin?.isNotEmpty == true))
                      const SizedBox(width: 16),
                    if (vehicle.vin?.isNotEmpty == true)
                      Expanded(
                        child: _buildDetailItem(
                          context,
                          icon: Icons.qr_code_rounded,
                          label: 'رقم الهيكل',
                          value: vehicle.vin!,
                        ),
                      ),
                  ],
                ),
              ],

              // Service status indicator
              if (_getServiceStatus() != null) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getServiceStatusColor(context).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: _getServiceStatusColor(context).withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        _getServiceStatusIcon(),
                        color: _getServiceStatusColor(context),
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _getServiceStatus()!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: _getServiceStatusColor(context),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: 6),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  String? _getServiceStatus() {
    final now = DateTime.now();
    
    if (vehicle.insuranceExpiry != null && vehicle.insuranceExpiry!.isBefore(now)) {
      return 'انتهت صلاحية التأمين';
    }
    
    if (vehicle.nextServiceDue != null && vehicle.nextServiceDue!.isBefore(now)) {
      return 'موعد الصيانة مستحق';
    }
    
    if (vehicle.nextServiceDue != null) {
      final daysUntilService = vehicle.nextServiceDue!.difference(now).inDays;
      if (daysUntilService <= 30) {
        return 'الصيانة قريباً ($daysUntilService يوم)';
      }
    }
    
    return null;
  }

  Color _getServiceStatusColor(BuildContext context) {
    final now = DateTime.now();
    
    if (vehicle.insuranceExpiry != null && vehicle.insuranceExpiry!.isBefore(now)) {
      return Theme.of(context).colorScheme.error;
    }
    
    if (vehicle.nextServiceDue != null && vehicle.nextServiceDue!.isBefore(now)) {
      return Theme.of(context).colorScheme.error;
    }
    
    if (vehicle.nextServiceDue != null) {
      final daysUntilService = vehicle.nextServiceDue!.difference(now).inDays;
      if (daysUntilService <= 30) {
        return AppColors.warning;
      }
    }
    
    return AppColors.success;
  }

  IconData _getServiceStatusIcon() {
    final now = DateTime.now();
    
    if (vehicle.insuranceExpiry != null && vehicle.insuranceExpiry!.isBefore(now)) {
      return Icons.warning_rounded;
    }
    
    if (vehicle.nextServiceDue != null && vehicle.nextServiceDue!.isBefore(now)) {
      return Icons.build_rounded;
    }
    
    if (vehicle.nextServiceDue != null) {
      final daysUntilService = vehicle.nextServiceDue!.difference(now).inDays;
      if (daysUntilService <= 30) {
        return Icons.schedule_rounded;
      }
    }
    
    return Icons.check_circle_rounded;
  }
}
