// ============================================================================
// VEHICLE DETAILS FORM - Forever Plan Architecture
// ============================================================================
// 
// Form widget for entering vehicle details (color, VIN, mileage, etc.).
// Following Forever Plan principles:
// - Material 3 Design System
// - Accessibility compliant
// - Performance optimized
// - User-friendly validation
//
// ============================================================================

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:carnow/core/theme/app_colors.dart';

class VehicleDetailsForm extends StatelessWidget {
  final GlobalKey<FormState> formKey;
  final TextEditingController colorController;
  final TextEditingController vinController;
  final TextEditingController licensePlateController;
  final TextEditingController mileageController;
  final TextEditingController notesController;
  final bool isPrimary;
  final ValueChanged<bool> onIsPrimaryChanged;

  const VehicleDetailsForm({
    super.key,
    required this.formKey,
    required this.colorController,
    required this.vinController,
    required this.licensePlateController,
    required this.mileageController,
    required this.notesController,
    required this.isPrimary,
    required this.onIsPrimaryChanged,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'تفاصيل السيارة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'أضف تفاصيل إضافية عن سيارتك (اختياري)',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 24),

            // Color field
            TextFormField(
              controller: colorController,
              decoration: InputDecoration(
                labelText: 'لون السيارة',
                hintText: 'مثال: أبيض، أسود، أحمر',
                prefixIcon: const Icon(Icons.palette_rounded),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              textInputAction: TextInputAction.next,
            ),

            const SizedBox(height: 16),

            // VIN field
            TextFormField(
              controller: vinController,
              decoration: InputDecoration(
                labelText: 'رقم الهيكل (VIN)',
                hintText: '17 رقم/حرف',
                prefixIcon: const Icon(Icons.qr_code_rounded),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                helperText: 'رقم الهيكل يساعد في العثور على قطع غيار دقيقة',
              ),
              textInputAction: TextInputAction.next,
              inputFormatters: [
                LengthLimitingTextInputFormatter(17),
                FilteringTextInputFormatter.allow(RegExp(r'[A-Z0-9]')),
              ],
              textCapitalization: TextCapitalization.characters,
              validator: (value) {
                if (value != null && value.isNotEmpty && value.length != 17) {
                  return 'رقم الهيكل يجب أن يكون 17 رقم/حرف';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // License plate field
            TextFormField(
              controller: licensePlateController,
              decoration: InputDecoration(
                labelText: 'رقم اللوحة',
                hintText: 'مثال: أ ب ج 123',
                prefixIcon: const Icon(Icons.confirmation_number_rounded),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              textInputAction: TextInputAction.next,
              textCapitalization: TextCapitalization.characters,
            ),

            const SizedBox(height: 16),

            // Mileage field
            TextFormField(
              controller: mileageController,
              decoration: InputDecoration(
                labelText: 'المسافة المقطوعة (كم)',
                hintText: 'مثال: 50000',
                prefixIcon: const Icon(Icons.speed_rounded),
                suffixText: 'كم',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              keyboardType: TextInputType.number,
              textInputAction: TextInputAction.next,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  final mileage = int.tryParse(value);
                  if (mileage == null || mileage < 0) {
                    return 'يرجى إدخال رقم صحيح';
                  }
                  if (mileage > 1000000) {
                    return 'المسافة كبيرة جداً';
                  }
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Notes field
            TextFormField(
              controller: notesController,
              decoration: InputDecoration(
                labelText: 'ملاحظات',
                hintText: 'أي ملاحظات إضافية عن السيارة',
                prefixIcon: const Icon(Icons.note_rounded),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              maxLines: 3,
              textInputAction: TextInputAction.done,
            ),

            const SizedBox(height: 24),

            // Primary vehicle switch
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primaryBrand.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.star_rounded,
                      color: AppColors.primaryBrand,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'السيارة الأساسية',
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          'ستظهر هذه السيارة أولاً وستُستخدم للتوصيات',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Switch(
                    value: isPrimary,
                    onChanged: onIsPrimaryChanged,
                    activeColor: AppColors.primaryBrand,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Info card
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.info.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.info.withOpacity(0.3),
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.info_rounded,
                    color: AppColors.info,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'نصيحة',
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            color: AppColors.info,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'كلما أضفت تفاصيل أكثر، كلما حصلت على توصيات أدق لقطع الغيار والمنتجات المناسبة لسيارتك.',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
