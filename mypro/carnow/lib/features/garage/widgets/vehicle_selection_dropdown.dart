// ============================================================================
// VEHICLE SELECTION DROPDOWN - Forever Plan Architecture
// ============================================================================
// 
// Generic dropdown widget for vehicle selection with search functionality.
// Following Forever Plan principles:
// - REAL DATA ONLY from providers
// - NO MOCK DATA tolerance
// - Material 3 Design System
// - Accessibility compliant
// - Performance optimized
//
// ============================================================================

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:carnow/core/theme/app_colors.dart';


class VehicleSelectionDropdown<T> extends ConsumerStatefulWidget {
  final String hint;
  final T? value;
  final ValueChanged<T?> onChanged;
  final ProviderBase itemsProvider;
  final T Function(dynamic) itemBuilder;
  final bool searchable;
  final String? searchHint;
  final bool allowClear;

  const VehicleSelectionDropdown({
    super.key,
    required this.hint,
    required this.value,
    required this.onChanged,
    required this.itemsProvider,
    required this.itemBuilder,
    this.searchable = false,
    this.searchHint,
    this.allowClear = false,
  });

  @override
  ConsumerState<VehicleSelectionDropdown<T>> createState() => _VehicleSelectionDropdownState<T>();
}

class _VehicleSelectionDropdownState<T> extends ConsumerState<VehicleSelectionDropdown<T>> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final itemsAsync = ref.watch(widget.itemsProvider as ProviderListenable);

    return itemsAsync.when(
      data: (items) {
        final List<dynamic> itemsList = items as List<dynamic>;
        
        // Filter items based on search query
        final filteredItems = widget.searchable && _searchQuery.isNotEmpty
            ? itemsList.where((item) {
                final itemValue = widget.itemBuilder(item);
                return itemValue.toString().toLowerCase().contains(_searchQuery.toLowerCase());
              }).toList()
            : itemsList;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search field (if searchable)
            if (widget.searchable) ...[
              TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: widget.searchHint ?? 'ابحث...',
                  prefixIcon: const Icon(Icons.search_rounded),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          onPressed: () {
                            _searchController.clear();
                            setState(() => _searchQuery = '');
                          },
                          icon: const Icon(Icons.clear_rounded),
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
                onChanged: (value) {
                  setState(() => _searchQuery = value);
                },
              ),
              const SizedBox(height: 8),
            ],

            // Dropdown
            DropdownButtonFormField<T>(
              value: widget.value,
              hint: Text(
                widget.hint,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                suffixIcon: widget.allowClear && widget.value != null
                    ? IconButton(
                        onPressed: () => widget.onChanged(null),
                        icon: const Icon(Icons.clear_rounded),
                      )
                    : null,
              ),
              items: filteredItems.map<DropdownMenuItem<T>>((item) {
                final itemValue = widget.itemBuilder(item);
                return DropdownMenuItem<T>(
                  value: itemValue,
                  child: Text(
                    itemValue.toString(),
                    style: Theme.of(context).textTheme.bodyMedium,
                    overflow: TextOverflow.ellipsis,
                  ),
                );
              }).toList(),
              onChanged: widget.onChanged,
              isExpanded: true,
              icon: Icon(
                Icons.keyboard_arrow_down_rounded,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),

            // No results message
            if (widget.searchable && _searchQuery.isNotEmpty && filteredItems.isEmpty) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.search_off_rounded,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'لا توجد نتائج للبحث "$_searchQuery"',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        );
      },
      loading: () => Container(
        height: 56,
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).colorScheme.outline,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: SizedBox(
            height: 20,
            width: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        ),
      ),
      error: (error, stackTrace) => Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.errorContainer.withOpacity(0.3),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Theme.of(context).colorScheme.error.withOpacity(0.3),
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.error_rounded,
              color: Theme.of(context).colorScheme.error,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'خطأ في تحميل البيانات',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
            ),
            TextButton(
              onPressed: () => ref.invalidate(widget.itemsProvider),
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }
}
