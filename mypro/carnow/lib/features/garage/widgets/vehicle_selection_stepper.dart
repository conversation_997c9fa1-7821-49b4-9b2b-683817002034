// ============================================================================
// VEHICLE SELECTION STEPPER - Forever Plan Architecture
// ============================================================================
// 
// Hierarchical vehicle selection widget (Make → Model → Year → Trim → Engine).
// Following Forever Plan principles:
// - REAL DATA ONLY from providers
// - NO MOCK DATA tolerance
// - Material 3 Design System
// - Accessibility compliant
// - Performance optimized
//
// ============================================================================

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:carnow/core/theme/app_colors.dart';

import 'package:carnow/features/garage/providers/garage_providers.dart';
import 'package:carnow/features/garage/widgets/vehicle_selection_dropdown.dart';
import 'package:carnow/features/garage/models/garage_models.dart';

class VehicleSelectionStepper extends ConsumerWidget {
  final VoidCallback? onSelectionComplete;

  const VehicleSelectionStepper({
    super.key,
    this.onSelectionComplete,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final vehicleSelection = ref.watch(vehicleSelectionStateProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'اختر سيارتك',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اختر ماركة وموديل سيارتك للحصول على قطع غيار متوافقة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),

          // Step 1: Vehicle Make
          _buildSelectionStep(
            context,
            stepNumber: 1,
            title: 'الماركة',
            isRequired: true,
            isCompleted: vehicleSelection.selectedMake != null,
            child: Consumer(
              builder: (context, ref, child) {
                final makesAsync = ref.watch(vehicleMakesProvider());
                return makesAsync.when(
                  data: (makes) => DropdownButtonFormField<VehicleMake>(
                    value: vehicleSelection.selectedMake,
                    hint: const Text('اختر ماركة السيارة'),
                    items: makes.map((make) => DropdownMenuItem(
                      value: make,
                      child: Text(make.name),
                    )).toList(),
                    onChanged: (make) {
                      if (make != null) {
                        ref.read(vehicleSelectionStateProvider.notifier).selectMake(make);
                      }
                    },
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                    ),
                  ),
                  loading: () => const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => Text('خطأ: $error'),
                );
              },
            ),
          ),

          const SizedBox(height: 16),

          // Step 2: Vehicle Model
          _buildSelectionStep(
            context,
            stepNumber: 2,
            title: 'الموديل',
            isRequired: true,
            isCompleted: vehicleSelection.selectedModel != null,
            isEnabled: vehicleSelection.selectedMake != null,
            child: vehicleSelection.selectedMake != null
                ? VehicleSelectionDropdown<String>(
                    hint: 'اختر موديل السيارة',
                    value: vehicleSelection.selectedModel?.name,
                    onChanged: (value) {
                      // Find the model by name and select it
                      ref.read(vehicleModelsProvider(vehicleSelection.selectedMake!.id)).whenData((models) {
                        final model = models.firstWhere((m) => m.name == value);
                        ref.read(vehicleSelectionStateProvider.notifier).selectModel(model);
                      });
                    },
                    itemsProvider: vehicleModelsProvider(vehicleSelection.selectedMake!.id),
                    itemBuilder: (model) => model.name,
                    searchable: true,
                    searchHint: 'ابحث عن الموديل...',
                  )
                : Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'اختر الماركة أولاً',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
          ),

          const SizedBox(height: 16),

          // Step 3: Vehicle Year (Optional)
          _buildSelectionStep(
            context,
            stepNumber: 3,
            title: 'السنة',
            isRequired: false,
            isCompleted: vehicleSelection.selectedYear != null,
            isEnabled: vehicleSelection.selectedModel != null,
            child: vehicleSelection.selectedModel != null
                ? VehicleSelectionDropdown<int>(
                    hint: 'اختر سنة السيارة (اختياري)',
                    value: vehicleSelection.selectedYear?.year,
                    onChanged: (value) {
                      if (value != null) {
                        ref.read(vehicleYearsProvider(vehicleSelection.selectedModel!.id)).whenData((years) {
                          final year = years.firstWhere((y) => y.year == value);
                          ref.read(vehicleSelectionStateProvider.notifier).selectYear(year);
                        });
                      } else {
                        ref.read(vehicleSelectionStateProvider.notifier).selectYear(null as dynamic);
                      }
                    },
                    itemsProvider: vehicleYearsProvider(vehicleSelection.selectedModel!.id),
                    itemBuilder: (year) => year.year,
                    allowClear: true,
                  )
                : Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'اختر الموديل أولاً',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
          ),

          const SizedBox(height: 16),

          // Step 4: Vehicle Trim (Optional)
          _buildSelectionStep(
            context,
            stepNumber: 4,
            title: 'الفئة',
            isRequired: false,
            isCompleted: vehicleSelection.selectedTrim != null,
            isEnabled: vehicleSelection.selectedModel != null,
            child: vehicleSelection.selectedModel != null
                ? VehicleSelectionDropdown<String>(
                    hint: 'اختر فئة السيارة (اختياري)',
                    value: vehicleSelection.selectedTrim?.name,
                    onChanged: (value) {
                      if (value != null) {
                        ref.read(vehicleTrimsProvider(vehicleSelection.selectedModel!.id)).whenData((trims) {
                          final trim = trims.firstWhere((t) => t.name == value);
                          ref.read(vehicleSelectionStateProvider.notifier).selectTrim(trim);
                        });
                      } else {
                        ref.read(vehicleSelectionStateProvider.notifier).selectTrim(null as dynamic);
                      }
                    },
                    itemsProvider: vehicleTrimsProvider(vehicleSelection.selectedModel!.id),
                    itemBuilder: (trim) => trim.name,
                    allowClear: true,
                    searchable: true,
                    searchHint: 'ابحث عن الفئة...',
                  )
                : Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'اختر الموديل أولاً',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
          ),

          const SizedBox(height: 16),

          // Step 5: Vehicle Engine (Optional)
          _buildSelectionStep(
            context,
            stepNumber: 5,
            title: 'المحرك',
            isRequired: false,
            isCompleted: vehicleSelection.selectedEngine != null,
            isEnabled: vehicleSelection.selectedModel != null,
            child: vehicleSelection.selectedModel != null
                ? VehicleSelectionDropdown<String>(
                    hint: 'اختر محرك السيارة (اختياري)',
                    value: vehicleSelection.selectedEngine?.engineCode,
                    onChanged: (value) {
                      if (value != null) {
                        ref.read(vehicleEnginesProvider(vehicleSelection.selectedModel!.id)).whenData((engines) {
                          final engine = engines.firstWhere((e) => e.engineCode == value);
                          ref.read(vehicleSelectionStateProvider.notifier).selectEngine(engine);
                        });
                      } else {
                        ref.read(vehicleSelectionStateProvider.notifier).selectEngine(null as dynamic);
                      }
                    },
                    itemsProvider: vehicleEnginesProvider(vehicleSelection.selectedModel!.id),
                    itemBuilder: (engine) => '${engine.engineCode} - ${engine.displacement}L ${engine.powerHp}HP',
                    allowClear: true,
                    searchable: true,
                    searchHint: 'ابحث عن المحرك...',
                  )
                : Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'اختر الموديل أولاً',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
          ),

          const SizedBox(height: 24),

          // Selection summary
          if (ref.read(vehicleSelectionStateProvider.notifier).canCreateVehicle) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.success.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.success.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.check_circle_rounded,
                        color: AppColors.success,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'اختيارك:',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          color: AppColors.success,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _buildSelectionSummary(vehicleSelection),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            if (onSelectionComplete != null)
              SizedBox(
                width: double.infinity,
                child: FilledButton(
                  onPressed: onSelectionComplete,
                  style: FilledButton.styleFrom(
                    backgroundColor: AppColors.primaryBrand,
                  ),
                  child: const Text('التالي'),
                ),
              ),
          ],
        ],
      ),
    );
  }

  Widget _buildSelectionStep(
    BuildContext context, {
    required int stepNumber,
    required String title,
    required bool isRequired,
    required bool isCompleted,
    bool isEnabled = true,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: isCompleted
                    ? AppColors.success
                    : isEnabled
                        ? AppColors.primaryBrand.withOpacity(0.1)
                        : Theme.of(context).colorScheme.surfaceVariant,
                shape: BoxShape.circle,
                border: Border.all(
                  color: isCompleted
                      ? AppColors.success
                      : isEnabled
                          ? AppColors.primaryBrand
                          : Theme.of(context).colorScheme.outline,
                ),
              ),
              child: isCompleted
                  ? Icon(
                      Icons.check_rounded,
                      size: 16,
                      color: Colors.white,
                    )
                  : Center(
                      child: Text(
                        stepNumber.toString(),
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          color: isEnabled
                              ? AppColors.primaryBrand
                              : Theme.of(context).colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: isEnabled
                    ? Theme.of(context).colorScheme.onSurface
                    : Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            if (isRequired) ...[
              const SizedBox(width: 4),
              Text(
                '*',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.error,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),
        child,
      ],
    );
  }

  String _buildSelectionSummary(VehicleSelection selection) {
    final parts = <String>[];
    
    if (selection.selectedMake != null) {
      parts.add(selection.selectedMake!.name);
    }
    
    if (selection.selectedModel != null) {
      parts.add(selection.selectedModel!.name);
    }
    
    if (selection.selectedYear != null) {
      parts.insert(0, selection.selectedYear!.year.toString());
    }
    
    if (selection.selectedTrim != null) {
      parts.add(selection.selectedTrim!.name);
    }
    
    return parts.join(' ');
  }
}
