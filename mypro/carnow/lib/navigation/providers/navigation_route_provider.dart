import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../features/account/screens/unified_profile_screen.dart';
import '../../features/categories/screens/category_browser_screen.dart';
import '../../features/categories/screens/category_products_screen.dart';
import '../../features/products/listings/screens/home_screen.dart';
import '../../features/products/listings/screens/product_details_screen.dart';
import '../../features/store/screens/store_screen.dart';
import '../../features/search/screens/search_screen.dart';
import '../../features/search/screens/search_results_screen.dart';
import '../models/navigation_route.dart';

final navigationRouteProvider = Provider<List<NavigationRoute>>((ref) {
  return [
    // 🏠 الصفحة الرئيسية
    NavigationRoute(
      path: '/',
      route: GoRoute(
        path: '/',
        builder: (context, state) => const HomeScreen(),
        routes: [
          GoRoute(
            path: 'product/:id',
            builder: (context, state) {
              final productId = state.pathParameters['id']!;
              return ProductDetailsScreen(productId: productId);
            },
          ),
        ],
      ),
    ),

    // 🗂️ الفئات
    NavigationRoute(
      path: '/categories',
      route: GoRoute(
        path: '/categories',
        builder: (context, state) => const CategoryBrowserScreen(),
        routes: [
          GoRoute(
            path: ':slug',
            builder: (context, state) {
              final slug = state.pathParameters['slug']!;
              return CategoryProductsScreen(categorySlug: slug);
            },
          ),
        ],
      ),
    ),

    // 🔍 البحث
    NavigationRoute(
      path: '/search',
      route: GoRoute(
        path: '/search',
        builder: (context, state) =>
            SearchScreen(initialQuery: state.uri.queryParameters['q']),
        routes: [
          GoRoute(
            path: 'results',
            builder: (context, state) {
              final query = state.uri.queryParameters['q'] ?? '';
              return SearchResultsScreen(searchQuery: query);
            },
          ),
        ],
      ),
    ),

    // NOTE: تم حذف مسارات "المفضلة" و"الجراج" من عناصر الشريط السفلي حسب طلب التصميم.
    // لكن المرآب متاح كصفحة منفصلة عبر /garage

    // 🚗 المرآب (متاح كصفحة منفصلة)
    // NavigationRoute(
    //   path: '/garage',
    //   route: GoRoute(
    //     path: '/garage',
    //     builder: (context, state) => const GarageScreen(),
    //   ),
    // ),

    // 🏪 المتجر
    NavigationRoute(
      path: '/store',
      route: GoRoute(
        path: '/store',
        builder: (context, state) => const StoreScreen(),
      ),
    ),

    // 👤 الحساب
    NavigationRoute(
      path: '/account',
      route: GoRoute(
        path: '/account',
        builder: (context, state) => const UnifiedProfileScreen(),
      ),
    ),
  ];
});
