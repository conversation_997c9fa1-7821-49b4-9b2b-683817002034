#!/bin/bash

# 🛑 CarNow Server Stopper Script 🛑
# إيقاف السيرفر المحلي بطريقة آمنة

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo -e "${YELLOW}"
echo "🛑 Stopping CarNow Local Server..."
echo -e "${NC}"

# Stop server by PID file
if [[ -f backend.pid ]]; then
    PID=$(cat backend.pid)
    if kill -0 "$PID" 2>/dev/null; then
        kill "$PID"
        print_success "Server stopped (PID: $PID)"
    else
        print_warning "Server was not running (PID: $PID)"
    fi
    rm -f backend.pid
else
    print_info "No PID file found"
fi

# Kill any remaining processes on port 8080
if lsof -ti:8080 >/dev/null 2>&1; then
    print_warning "Killing remaining processes on port 8080..."
    lsof -ti:8080 | xargs kill -9 2>/dev/null || true
    print_success "Port 8080 cleared"
fi

# Restore .env backup if exists
if [[ -f .env.backup ]]; then
    print_info "Restoring original .env file..."
    mv .env.backup .env
    print_success "Original .env restored"
fi

print_success "All done! Server stopped safely."
