// ============================================================================
// GARAGE BASIC TESTS - Forever Plan Architecture
// ============================================================================
// 
// Basic functionality tests for the garage feature.
// Following Forever Plan principles:
// - Real data testing (no mock data)
// - Model validation
// - Navigation testing
//
// ============================================================================

import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/features/garage/navigation/garage_navigation.dart';
import 'package:carnow/features/garage/models/garage_models.dart';

void main() {
  group('Garage Basic Tests', () {
    group('GarageNavigation Tests', () {
      test('should have correct route paths', () {
        expect(GarageNavigation.garage, equals('/garage'));
        expect(GarageNavigation.addVehicle, equals('/garage/add'));
        expect(GarageNavigation.vehicleDetails, equals('/garage/vehicle'));
        expect(GarageNavigation.editVehicle, equals('/edit'));
      });

      test('should extract vehicle ID from route correctly', () {
        const route = '/garage/vehicle/123';
        final vehicleId = GarageNavigation.getVehicleIdFromRoute(route);
        expect(vehicleId, equals('123'));
      });

      test('should return null for invalid route', () {
        const route = '/invalid/route';
        final vehicleId = GarageNavigation.getVehicleIdFromRoute(route);
        expect(vehicleId, isNull);
      });

      test('should identify garage routes correctly', () {
        expect(GarageNavigation.isGarageRoute('/garage'), isTrue);
        expect(GarageNavigation.isGarageRoute('/garage/add'), isTrue);
        expect(GarageNavigation.isGarageRoute('/garage/vehicle/123'), isTrue);
        expect(GarageNavigation.isGarageRoute('/home'), isFalse);
      });
    });

    group('Data Models Tests', () {
      test('UserVehicle should create correctly', () {
        final vehicle = UserVehicle(
          id: 1,
          userId: 'user123',
          make: 'Toyota',
          model: 'Camry',
          year: 2023,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(vehicle.id, equals(1));
        expect(vehicle.userId, equals('user123'));
        expect(vehicle.make, equals('Toyota'));
        expect(vehicle.model, equals('Camry'));
        expect(vehicle.year, equals(2023));
        expect(vehicle.mileage, equals(0)); // Default value
        expect(vehicle.condition, equals('good')); // Default value
      });

      test('VehicleMake should create correctly', () {
        const make = VehicleMake(
          id: 1,
          name: 'Toyota',
          logoUrl: 'https://example.com/toyota.png',
        );

        expect(make.id, equals(1));
        expect(make.name, equals('Toyota'));
        expect(make.logoUrl, equals('https://example.com/toyota.png'));
      });

      test('VehicleModel should create correctly', () {
        const model = VehicleModel(
          id: 1,
          makeId: 1,
          name: 'Camry',
        );

        expect(model.id, equals(1));
        expect(model.makeId, equals(1));
        expect(model.name, equals('Camry'));
      });

      test('VehicleYear should create correctly', () {
        const year = VehicleYear(
          id: 1,
          modelId: 1,
          year: 2023,
        );

        expect(year.id, equals(1));
        expect(year.modelId, equals(1));
        expect(year.year, equals(2023));
      });

      test('VehicleTrim should create correctly', () {
        const trim = VehicleTrim(
          id: 1,
          modelId: 1,
          name: 'Sport',
        );

        expect(trim.id, equals(1));
        expect(trim.modelId, equals(1));
        expect(trim.name, equals('Sport'));
      });

      test('VehicleEngine should create correctly', () {
        const engine = VehicleEngine(
          id: 1,
          modelId: 1,
          displacementCc: 2000,
          cylinders: 4,
          powerHp: 200,
          torqueNm: 300,
        );

        expect(engine.id, equals(1));
        expect(engine.modelId, equals(1));
        expect(engine.displacementCc, equals(2000));
        expect(engine.cylinders, equals(4));
        expect(engine.powerHp, equals(200));
        expect(engine.torqueNm, equals(300));
        expect(engine.isTurbo, isFalse); // Default value
      });

      test('UserVehicle should handle optional fields correctly', () {
        final vehicle = UserVehicle(
          id: 1,
          userId: 'user123',
          make: 'Toyota',
          model: 'Camry',
          year: 2023,
          color: 'Red',
          vin: 'ABC123',
          licensePlate: 'XYZ789',
          mileage: 50000,
          condition: 'excellent',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(vehicle.color, equals('Red'));
        expect(vehicle.vin, equals('ABC123'));
        expect(vehicle.licensePlate, equals('XYZ789'));
        expect(vehicle.mileage, equals(50000));
        expect(vehicle.condition, equals('excellent'));
      });

      test('VehicleEngine should handle optional fields correctly', () {
        const engine = VehicleEngine(
          id: 1,
          modelId: 1,
          engineCode: 'ABC123',
          displacementCc: 2000,
          cylinders: 4,
          powerHp: 200,
          torqueNm: 300,
          fuelType: 'Gasoline',
          isTurbo: true,
          engineFamily: 'DOHC',
          valveTrain: '16V',
          description: 'High performance engine',
        );

        expect(engine.engineCode, equals('ABC123'));
        expect(engine.fuelType, equals('Gasoline'));
        expect(engine.isTurbo, isTrue);
        expect(engine.engineFamily, equals('DOHC'));
        expect(engine.valveTrain, equals('16V'));
        expect(engine.description, equals('High performance engine'));
      });
    });

    group('Model Validation Tests', () {
      test('UserVehicle should require essential fields', () {
        expect(() => UserVehicle(
          id: 1,
          userId: 'user123',
          make: 'Toyota',
          model: 'Camry',
          year: 2023,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ), returnsNormally);
      });

      test('VehicleMake should require essential fields', () {
        expect(() => const VehicleMake(
          id: 1,
          name: 'Toyota',
        ), returnsNormally);
      });

      test('VehicleModel should require essential fields', () {
        expect(() => const VehicleModel(
          id: 1,
          makeId: 1,
          name: 'Camry',
        ), returnsNormally);
      });

      test('VehicleYear should require essential fields', () {
        expect(() => const VehicleYear(
          id: 1,
          modelId: 1,
          year: 2023,
        ), returnsNormally);
      });

      test('VehicleTrim should require essential fields', () {
        expect(() => const VehicleTrim(
          id: 1,
          modelId: 1,
          name: 'Sport',
        ), returnsNormally);
      });

      test('VehicleEngine should require essential fields', () {
        expect(() => const VehicleEngine(
          id: 1,
          modelId: 1,
        ), returnsNormally);
      });
    });
  });
}
