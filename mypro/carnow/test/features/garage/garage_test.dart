// ============================================================================
// GARAGE TESTS - Forever Plan Architecture
// ============================================================================
// 
// Basic tests for the garage feature.
// Following Forever Plan principles:
// - Real data testing (no mock data)
// - Integration testing
// - Performance testing
// - Accessibility testing
//
// ============================================================================

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:carnow/features/garage/screens/garage_screen.dart';
import 'package:carnow/features/garage/screens/add_vehicle_screen.dart';
import 'package:carnow/features/garage/navigation/garage_navigation.dart';
import 'package:carnow/features/garage/models/garage_models.dart';

void main() {
  group('Garage Feature Tests', () {
    testWidgets('GarageScreen should build without errors', (WidgetTester tester) async {
      // Test that the garage screen can be built
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const GarageScreen(),
          ),
        ),
      );

      // Verify the screen builds without throwing
      expect(find.byType(GarageScreen), findsOneWidget);
    });

    testWidgets('AddVehicleScreen should build without errors', (WidgetTester tester) async {
      // Test that the add vehicle screen can be built
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const AddVehicleScreen(),
          ),
        ),
      );

      // Verify the screen builds without throwing
      expect(find.byType(AddVehicleScreen), findsOneWidget);
    });

    group('GarageNavigation Tests', () {
      test('should have correct route paths', () {
        expect(GarageNavigation.garage, equals('/garage'));
        expect(GarageNavigation.addVehicle, equals('/garage/add'));
        expect(GarageNavigation.vehicleDetails, equals('/garage/vehicle'));
        expect(GarageNavigation.editVehicle, equals('/edit'));
      });

      test('should extract vehicle ID from route correctly', () {
        const route = '/garage/vehicle/123';
        final vehicleId = GarageNavigation.getVehicleIdFromRoute(route);
        expect(vehicleId, equals('123'));
      });

      test('should return null for invalid route', () {
        const route = '/invalid/route';
        final vehicleId = GarageNavigation.getVehicleIdFromRoute(route);
        expect(vehicleId, isNull);
      });

      test('should identify garage routes correctly', () {
        expect(GarageNavigation.isGarageRoute('/garage'), isTrue);
        expect(GarageNavigation.isGarageRoute('/garage/add'), isTrue);
        expect(GarageNavigation.isGarageRoute('/garage/vehicle/123'), isTrue);
        expect(GarageNavigation.isGarageRoute('/home'), isFalse);
      });
    });

    group('Data Models Tests', () {
      test('UserVehicle should create correctly', () {
        final vehicle = UserVehicle(
          id: 1,
          userId: 'user123',
          make: 'Toyota',
          model: 'Camry',
          year: 2023,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(vehicle.id, equals(1));
        expect(vehicle.userId, equals('user123'));
        expect(vehicle.make, equals('Toyota'));
        expect(vehicle.model, equals('Camry'));
        expect(vehicle.year, equals(2023));
        expect(vehicle.mileage, equals(0)); // Default value
        expect(vehicle.condition, equals('good')); // Default value
      });

      test('VehicleMake should create correctly', () {
        const make = VehicleMake(
          id: 1,
          name: 'Toyota',
          logoUrl: 'https://example.com/toyota.png',
        );

        expect(make.id, equals(1));
        expect(make.name, equals('Toyota'));
        expect(make.logoUrl, equals('https://example.com/toyota.png'));
      });

      test('VehicleModel should create correctly', () {
        const model = VehicleModel(
          id: 1,
          makeId: 1,
          name: 'Camry',
        );

        expect(model.id, equals(1));
        expect(model.makeId, equals(1));
        expect(model.name, equals('Camry'));
      });

      test('VehicleYear should create correctly', () {
        const year = VehicleYear(
          id: 1,
          modelId: 1,
          year: 2023,
        );

        expect(year.id, equals(1));
        expect(year.modelId, equals(1));
        expect(year.year, equals(2023));
      });

      test('VehicleTrim should create correctly', () {
        const trim = VehicleTrim(
          id: 1,
          modelId: 1,
          name: 'Sport',
        );

        expect(trim.id, equals(1));
        expect(trim.modelId, equals(1));
        expect(trim.name, equals('Sport'));
      });

      test('VehicleEngine should create correctly', () {
        const engine = VehicleEngine(
          id: 1,
          modelId: 1,
          displacementCc: 2000,
          cylinders: 4,
          powerHp: 200,
          torqueNm: 300,
        );

        expect(engine.id, equals(1));
        expect(engine.modelId, equals(1));
        expect(engine.displacementCc, equals(2000));
        expect(engine.cylinders, equals(4));
        expect(engine.powerHp, equals(200));
        expect(engine.torqueNm, equals(300));
        expect(engine.isTurbo, isFalse); // Default value
      });
    });

    group('Performance Tests', () {
      testWidgets('GarageScreen should render within performance budget', (WidgetTester tester) async {
        final stopwatch = Stopwatch()..start();

        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp(
              home: const GarageScreen(),
            ),
          ),
        );

        await tester.pumpAndSettle();
        stopwatch.stop();

        // Should render within 100ms (performance budget)
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
      });
    });

    group('Accessibility Tests', () {
      testWidgets('GarageScreen should be accessible', (WidgetTester tester) async {
        final SemanticsHandle handle = tester.ensureSemantics();

        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp(
              home: const GarageScreen(),
            ),
          ),
        );

        await expectLater(tester, meetsGuideline(androidTapTargetGuideline));
        await expectLater(tester, meetsGuideline(iOSTapTargetGuideline));
        await expectLater(tester, meetsGuideline(labeledTapTargetGuideline));
        await expectLater(tester, meetsGuideline(textContrastGuideline));

        handle.dispose();
      });

      testWidgets('AddVehicleScreen should be accessible', (WidgetTester tester) async {
        final SemanticsHandle handle = tester.ensureSemantics();

        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp(
              home: const AddVehicleScreen(),
            ),
          ),
        );

        await expectLater(tester, meetsGuideline(androidTapTargetGuideline));
        await expectLater(tester, meetsGuideline(iOSTapTargetGuideline));
        await expectLater(tester, meetsGuideline(labeledTapTargetGuideline));
        await expectLater(tester, meetsGuideline(textContrastGuideline));

        handle.dispose();
      });
    });
  });
}
